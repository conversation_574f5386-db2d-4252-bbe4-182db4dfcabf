import json
import logging
import os
import sys
import time
from pathlib import Path

from rich.console import Console
from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.panel import Panel
from rich.text import Text


class DuplicateTracebackFilter(logging.Filter):
    """Filter to prevent duplicate traceback logging."""

    def __init__(self):
        super().__init__()
        self.seen_exceptions = {}  # Changed to dict to store timestamps
        self.exception_window = 60  # seconds
        self.last_cleanup = time.time()
        self.max_duplicates = 3  # Allow up to 3 duplicates before filtering

    def filter(self, record):
        # Clean up old exceptions periodically
        current_time = time.time()
        if current_time - self.last_cleanup > self.exception_window:
            self._cleanup_old_exceptions(current_time)
            self.last_cleanup = current_time

        # If this is an exception record
        if record.exc_info:
            exc_type, exc_value, exc_traceback = record.exc_info

            # Create a unique identifier for this exception
            exc_id = self._create_exception_id(exc_type, exc_value, record)

            # Check if we've seen this exact exception recently
            if exc_id in self.seen_exceptions:
                count, first_seen = self.seen_exceptions[exc_id]

                # If we've exceeded the duplicate threshold
                if count >= self.max_duplicates:
                    # Convert to simple error message without traceback
                    record.exc_info = None
                    record.msg = f"[FILTERED DUPLICATE #{count}] {record.msg} (first seen {int(current_time - first_seen)}s ago)"
                    self.seen_exceptions[exc_id] = (count + 1, first_seen)
                    return True
                else:
                    # Still allow some duplicates but mark them
                    self.seen_exceptions[exc_id] = (count + 1, first_seen)
                    record.msg = f"[DUPLICATE #{count + 1}] {record.msg}"
            else:
                # First time seeing this exception
                self.seen_exceptions[exc_id] = (1, current_time)

        return True

    def _create_exception_id(self, exc_type, exc_value, record):
        """Create a unique identifier for an exception."""
        # Include exception type, message, and location
        exc_str = str(exc_value)

        # For chained exceptions, focus on the root cause
        # Check if exc_value is not None before accessing __cause__
        if (
            exc_value is not None
            and hasattr(exc_value, "__cause__")
            and exc_value.__cause__
        ):
            exc_str = (
                str(exc_value.__cause__) if exc_value.__cause__ is not None else ""
            )

        return (
            exc_type.__name__,
            exc_str[:200],  # Limit message length
            record.pathname,
            record.funcName,
        )

    def _cleanup_old_exceptions(self, current_time):
        """Remove old exception records to prevent memory leaks."""
        expired_keys = [
            key
            for key, (count, timestamp) in self.seen_exceptions.items()
            if current_time - timestamp > self.exception_window
        ]
        for key in expired_keys:
            del self.seen_exceptions[key]


class ExceptionChainFilter(logging.Filter):
    """Filter to handle exception chaining and prevent cascading duplicates."""

    def __init__(self):
        super().__init__()
        self.active_chains = {}  # Track active exception chains
        self.chain_timeout = 5  # seconds

    def filter(self, record):
        if not record.exc_info:
            return True

        current_time = time.time()
        exc_type, exc_value, exc_traceback = record.exc_info

        # Clean up old chains
        self._cleanup_old_chains(current_time)

        # Check if this is part of an exception chain
        chain_id = self._get_chain_id(exc_value)

        if chain_id in self.active_chains:
            # This is a chained exception - suppress it
            record.exc_info = None
            record.msg = f"[CHAINED EXCEPTION SUPPRESSED] {record.msg}"
            return True
        else:
            # New exception chain - record it
            self.active_chains[chain_id] = current_time

            # If this exception has a cause, mark the chain
            if (
                exc_value is not None
                and hasattr(exc_value, "__cause__")
                and exc_value.__cause__
            ):
                cause_chain_id = (
                    self._get_chain_id(exc_value.__cause__)
                    if exc_value.__cause__ is not None
                    else ""
                )
                self.active_chains[cause_chain_id] = current_time

        return True

    def _get_chain_id(self, exc_value):
        """Get a unique identifier for an exception chain."""
        return (type(exc_value).__name__, str(exc_value)[:100])

    def _cleanup_old_chains(self, current_time):
        """Remove old exception chains."""
        expired_chains = [
            chain_id
            for chain_id, timestamp in self.active_chains.items()
            if current_time - timestamp > self.chain_timeout
        ]
        for chain_id in expired_chains:
            del self.active_chains[chain_id]


class LoggerConfig:
    """Production-ready logger configuration with Rich integration."""

    def __init__(self):
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        self.log_dir = Path(os.getenv("LOG_DIR", "logs"))
        self.enable_file_logging = (
            os.getenv("ENABLE_FILE_LOGGING", "true").lower() == "true"
        )
        self.enable_json_logging = (
            os.getenv("ENABLE_JSON_LOGGING", "false").lower() == "true"
        )
        self.diagnose = os.getenv("LOG_DIAGNOSE", "true").lower() == "true"
        self.backtrace = os.getenv("LOG_BACKTRACE", "false").lower() == "true"
        self.auto_filter_duplicates = (
            os.getenv("AUTO_FILTER_DUPLICATES", "true").lower() == "true"
        )

        # Get terminal width for optimal display
        terminal_width = 240

        # Setup rich console with forced settings for FastAPI/production
        self.error_console = Console(
            stderr=True,
            style="bold red",
            force_terminal=True,
            force_interactive=True,
            width=terminal_width,
            color_system="truecolor",
        )
        self.main_console = Console(
            force_terminal=True,
            force_interactive=True,
            width=terminal_width,
            color_system="truecolor",
        )

        self.terminal_width = terminal_width

    def setup_logger(self) -> logging.Logger:
        """Configure logger with Rich handlers and exception formatting."""
        # Clear any existing handlers
        logging.root.handlers = []

        # Setup rich traceback handler globally
        self._setup_rich_traceback()

        # Create main logger
        logger = logging.getLogger("app")
        logger.setLevel(self.log_level)
        logger.handlers = []

        # Setup Rich console handler
        self._setup_rich_handler(logger)

        # Setup file logging if enabled
        if self.enable_file_logging:
            self._setup_file_logging(logger)

        # Configure intercepted loggers
        self._setup_intercept_handlers()

        # Configure custom exception handling
        self._setup_custom_exception_handler()

        return logger

    def _setup_rich_traceback(self) -> None:
        """Setup rich traceback handler with enhanced styling."""
        # Don't install global traceback handler to avoid duplicates
        # We'll handle tracebacks manually in exception handlers
        pass

    def _setup_rich_handler(self, logger: logging.Logger) -> None:
        """Setup Rich console handler for beautiful terminal output."""
        rich_handler = RichHandler(
            console=self.main_console,
            show_time=True,
            show_path=True,
            show_level=True,
            rich_tracebacks=True,
            tracebacks_show_locals=self.diagnose,
            tracebacks_max_frames=20,
            tracebacks_theme="monokai",
            tracebacks_width=self.terminal_width,  # Use full terminal width for tracebacks
            markup=True,
            keywords=[],
            enable_link_path=False,  # Disable file links for server environments
        )

        # Custom formatter for Rich handler with file path filtering
        class CustomRichFormatter(logging.Formatter):
            def format(self, record):
                # For API access logs from logger.py, hide the file path
                if (
                    record.name == "app"
                    and record.pathname
                    and "logger.py" in record.pathname
                ):
                    # Hide file path for logger.py (API logs)
                    record.pathname = ""
                    record.filename = ""
                    record.funcName = ""
                    record.lineno = 0
                elif record.pathname:
                    # Show full path starting from backend/ for other files
                    if "backend/" in record.pathname:
                        backend_index = record.pathname.find("backend/")
                        record.pathname = record.pathname[backend_index:]
                return super().format(record)

        rich_formatter = CustomRichFormatter(fmt="%(message)s", datefmt="[%X]")
        rich_handler.setFormatter(rich_formatter)
        rich_handler.setLevel(self.log_level)

        # Add duplicate filter if enabled
        if self.auto_filter_duplicates:
            duplicate_filter = DuplicateTracebackFilter()
            chain_filter = ExceptionChainFilter()
            rich_handler.addFilter(duplicate_filter)
            rich_handler.addFilter(chain_filter)

        logger.addHandler(rich_handler)

    def _setup_file_logging(self, logger: logging.Logger) -> None:
        """Setup file logging with rotation and compression."""
        # Create log directory if it doesn't exist
        self.log_dir.mkdir(exist_ok=True)

        # Standard file formatter
        file_formatter = logging.Formatter(
            fmt="%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d | %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S.%f",
        )

        # General application logs
        file_handler = logging.FileHandler(self.log_dir / "app.log")
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(self.log_level)
        logger.addHandler(file_handler)

        # Error logs (separate file)
        error_file_handler = logging.FileHandler(self.log_dir / "error.log")
        error_file_handler.setFormatter(file_formatter)
        error_file_handler.setLevel(logging.ERROR)
        logger.addHandler(error_file_handler)

        # JSON structured logs for production parsing
        if self.enable_json_logging:
            json_handler = logging.FileHandler(self.log_dir / "app.json")
            json_formatter = JsonFormatter()
            json_handler.setFormatter(json_formatter)
            json_handler.setLevel(self.log_level)
            logger.addHandler(json_handler)

    def _setup_intercept_handlers(self) -> None:
        """Setup interception of standard logging messages."""
        # List of loggers to intercept
        loggers_to_intercept = [
            "uvicorn",
            "uvicorn.access",
            "uvicorn.error",
            "fastapi",
            "starlette",
            "gunicorn",
            "gunicorn.access",
            "gunicorn.error",
            "celery",
            "celery.task",
        ]

        # List of loggers to suppress or set to higher levels
        loggers_to_suppress = {
            "watchfiles": "WARNING",
            "watchfiles.main": "WARNING",
            "watchfiles.watcher": "WARNING",
        }

        # Configure specific loggers and all existing ones
        for name in {*logging.root.manager.loggerDict.keys(), *loggers_to_intercept}:
            logger_instance = logging.getLogger(name)
            # Don't modify our main app logger
            if name != "app":
                logger_instance.handlers = []
                logger_instance.propagate = True

        # Configure suppressed loggers
        for logger_name, level in loggers_to_suppress.items():
            logger_instance = logging.getLogger(logger_name)
            logger_instance.setLevel(getattr(logging, level))

    def _setup_custom_exception_handler(self) -> None:
        """Setup custom exception handler with red bounding box."""

        def exception_hook(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return

            # Create a panel with red border for the traceback
            try:
                # Use rich console to print exception with red bounding box
                self.error_console.print(
                    Panel(
                        Text("💥 UNCAUGHT EXCEPTION 💥", style="bold white"),
                        border_style="bold red",
                        title="[bold red]ERROR[/bold red]",
                        title_align="center",
                        padding=(1, 2),
                    )
                )

                # Print the actual exception with rich formatting
                self.error_console.print_exception(
                    show_locals=self.diagnose,
                    max_frames=20,
                    extra_lines=3,
                    theme="monokai",
                    width=self.terminal_width,
                )

                # Add a closing panel
                self.error_console.print(
                    Panel(
                        Text("Application will now exit", style="bold white"),
                        border_style="bold red",
                        padding=(0, 2),
                    )
                )

            except Exception as e:
                # Fallback to standard exception handling if rich fails
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                self.error_console.print(
                    f"[bold red]Error in exception handler: {e}[/bold red]"
                )

        sys.excepthook = exception_hook

    def print_exception_with_red_box(self):
        """Manually print exception with red bounding box."""
        exc_type, _, _ = sys.exc_info()

        if exc_type is not None:
            # Create a panel with red border for the traceback
            self.error_console.print(
                Panel(
                    Text("🚨 EXCEPTION CAUGHT 🚨", style="bold white"),
                    border_style="bold red",
                    title="[bold red]ERROR[/bold red]",
                    title_align="center",
                    padding=(1, 2),
                )
            )

            # Print the actual exception with rich formatting
            self.error_console.print_exception(
                show_locals=self.diagnose,
                max_frames=20,
                extra_lines=3,
                theme="monokai",
                width=self.terminal_width,
            )


class HTTPStatusCodes:
    """HTTP status code to text translation."""

    STATUS_CODES: dict[int, str] = {
        # 1xx Informational
        100: "Continue",
        101: "Switching Protocols",
        102: "Processing",
        103: "Early Hints",
        # 2xx Success
        200: "OK",
        201: "Created",
        202: "Accepted",
        203: "Non-Authoritative Information",
        204: "No Content",
        205: "Reset Content",
        206: "Partial Content",
        207: "Multi-Status",
        208: "Already Reported",
        226: "IM Used",
        # 3xx Redirection
        300: "Multiple Choices",
        301: "Moved Permanently",
        302: "Found",
        303: "See Other",
        304: "Not Modified",
        305: "Use Proxy",
        307: "Temporary Redirect",
        308: "Permanent Redirect",
        # 4xx Client Error
        400: "Bad Request",
        401: "Unauthorized",
        402: "Payment Required",
        403: "Forbidden",
        404: "Not Found",
        405: "Method Not Allowed",
        406: "Not Acceptable",
        407: "Proxy Authentication Required",
        408: "Request Timeout",
        409: "Conflict",
        410: "Gone",
        411: "Length Required",
        412: "Precondition Failed",
        413: "Payload Too Large",
        414: "URI Too Long",
        415: "Unsupported Media Type",
        416: "Range Not Satisfiable",
        417: "Expectation Failed",
        418: "I'm a teapot",
        421: "Misdirected Request",
        422: "Unprocessable Entity",
        423: "Locked",
        424: "Failed Dependency",
        425: "Too Early",
        426: "Upgrade Required",
        428: "Precondition Required",
        429: "Too Many Requests",
        431: "Request Header Fields Too Large",
        451: "Unavailable For Legal Reasons",
        # 5xx Server Error
        500: "Internal Server Error",
        501: "Not Implemented",
        502: "Bad Gateway",
        503: "Service Unavailable",
        504: "Gateway Timeout",
        505: "HTTP Version Not Supported",
        506: "Variant Also Negotiates",
        507: "Insufficient Storage",
        508: "Loop Detected",
        510: "Not Extended",
        511: "Network Authentication Required",
    }

    @classmethod
    def get_status_text(cls, status_code: int) -> str:
        """Get the status text for a given status code."""
        return cls.STATUS_CODES.get(status_code, "Unknown")

    @classmethod
    def format_status_with_color(cls, status_code: int) -> str:
        """Format status code with Rich color based on status category."""
        status_text = cls.get_status_text(status_code)

        if 200 <= status_code < 300:
            return f"[green]{status_code} {status_text}[/green]"
        elif 300 <= status_code < 400:
            return f"[yellow]{status_code} {status_text}[/yellow]"
        elif 400 <= status_code < 500:
            return f"[orange3]{status_code} {status_text}[/orange3]"
        elif 500 <= status_code < 600:
            return f"[red]{status_code} {status_text}[/red]"
        else:
            return f"[white]{status_code} {status_text}[/white]"


class AccessLogger:
    """FastAPI/Uvicorn access logger with Rich formatting."""

    def __init__(self, logger: logging.Logger):
        self.logger = logger

    def log_request(
        self,
        client_ip: str,
        method: str,
        path: str,
        query_string: str,
        http_version: str,
        status_code: int,
        response_time: float,
        user_agent: str = "",
        referer: str = "",
    ):
        """Log HTTP request in access log format with Rich styling."""

        # Format query string
        full_path = f"{path}?{query_string}" if query_string else path

        # Format the request line with proper alignment
        method_padded = f"{method:<7}"  # Pad method to 7 chars (DELETE is longest)
        request_line = f'"{method_padded} {full_path} HTTP/{http_version}"'

        # Get colored status with consistent width
        status_text = HTTPStatusCodes.get_status_text(status_code)
        if 200 <= status_code < 300:
            colored_status = f"[green]{status_code:>3} {status_text:<30}[/green]"
        elif 300 <= status_code < 400:
            colored_status = f"[yellow]{status_code:>3} {status_text:<30}[/yellow]"
        elif 400 <= status_code < 500:
            colored_status = f"[orange3]{status_code:>3} {status_text:<30}[/orange3]"
        elif 500 <= status_code < 600:
            colored_status = f"[red]{status_code:>3} {status_text:<30}[/red]"
        else:
            colored_status = f"[white]{status_code:>3} {status_text:<30}[/white]"

        # Format response time with color and consistent width
        if response_time < 0.1:
            time_color = "[green]"
        elif response_time < 0.5:
            time_color = "[yellow]"
        elif response_time < 1.0:
            time_color = "[orange3]"
        else:
            time_color = "[red]"

        response_time_str = (
            f"{time_color}{response_time:>7.3f}s[/{time_color.strip('[').strip(']')}]"
        )

        # Create the access log message with improved spacing and alignment
        access_msg = f"[cyan]{client_ip:<15}[/cyan] {request_line:<100} {colored_status} {response_time_str}"

        # Add user agent if present
        if user_agent:
            access_msg += (
                f" │ [dim]UA: {user_agent[:50]}...[/dim]"
                if len(user_agent) > 50
                else f" │ [dim]UA: {user_agent}[/dim]"
            )

        # Log at INFO level
        self.logger.info(access_msg)


class JsonFormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""

    def format(self, record: logging.LogRecord) -> str:
        log_data = {
            "time": self.formatTime(record),
            "level": record.levelname,
            "name": record.name,
            "function": record.funcName,
            "line": record.lineno,
            "message": record.getMessage(),
            "process_id": os.getpid(),
            "thread_id": record.thread,
        }

        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_data)


# Initialize logger configuration
config = LoggerConfig()
logger = config.setup_logger()

# Initialize access logger
access_logger = AccessLogger(logger)


def exception_with_red_box(message: str) -> None:
    """Log exception with red bounding box display."""
    logger.error(message, exc_info=True)


# Export configured logger, config, access logger and custom function
__all__ = [
    "logger",
    "config",
    "access_logger",
    "exception_with_red_box",
    "HTTPStatusCodes",
]
