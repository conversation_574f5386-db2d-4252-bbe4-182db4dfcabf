
from fastapi import APIRouter, HTTPException

from app.logger import logger
from app.schemas.constants import (
    AllConstantsResponse,
    ConstantCategoriesResponse,
    ConstantCategory,
)
from app.services.constants_service import ConstantsService

router = APIRouter()


@router.get("/health-check/")
async def health_check() -> bool:
    return True


# Constants/Enums endpoints
@router.get("/constants/", response_model=ConstantCategoriesResponse)
async def get_constant_categories() -> ConstantCategoriesResponse:
    """
    Get all available constant categories.

    Returns a list of all available constant categories that can be queried.
    """
    try:
        return ConstantsService.get_all_categories()
    except Exception:
        logger.exception("Error getting constant categories.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while fetching constant categories.",
        )


@router.get("/constants/{category}", response_model=ConstantCategory)
async def get_constant_category(category: str) -> ConstantCategory:
    """
    Get constants for a specific category.

    Args:
        category: The category name (e.g., 'cloud_providers', 'aws_regions')

    Returns:
        Constants data for the specified category.
    """
    try:
        result = ConstantsService.get_category_data(category)
        if not result:
            raise HTTPException(
                status_code=404,
                detail=f"Category '{category}' not found.",
            )
        return result
    except HTTPException:
        raise
    except Exception:
        logger.exception(f"Error getting constants for category '{category}'.")
        raise HTTPException(
            status_code=500,
            detail=f"An unexpected error occurred while fetching constants for category '{category}'.",
        )


@router.get("/constants/all/", response_model=AllConstantsResponse)
async def get_all_constants() -> AllConstantsResponse:
    """
    Get all constants data at once.

    Returns:
        All available constants organized by category.
    """
    try:
        return ConstantsService.get_all_constants_data()
    except Exception:
        logger.exception("Error getting all constants data.")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while fetching all constants data.",
        )
