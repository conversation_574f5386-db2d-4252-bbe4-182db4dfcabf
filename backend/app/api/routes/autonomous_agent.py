import json
import uuid

from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Query
from fastapi.responses import StreamingResponse

from app.api.deps import CurrentUser, SessionAsyncDep, SessionDep
from app.core.langfuse import langfuse_handler
from app.logger import logger
from app.models import (
    ConversationCreateRequest,
    ConversationPublic,
    ConversationRenameRequest,
    ConversationsPublic,
    MessagePublicList,
    MessageStreamInput,
    StreamResponse,
)
from app.schemas.chat import ChatServiceContext
from app.services.agent import AgentService, validator
from app.services.conversation_service import ConversationService

router = APIRouter()


@router.post("/conversations", response_model=ConversationPublic)
async def create_conversation(
    current_user: CurrentUser,
    async_session: SessionAsyncDep,
    session: SessionDep,
    request: ConversationCreateRequest,
) -> ConversationPublic:
    """Create a new conversation."""
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )
    conversation = await conversation_service.create_conversation(
        agent_id=request.agent_id,
        workspace_id=current_user.current_workspace_id,
        resource_id=request.resource_id,
    )
    return ConversationPublic.model_validate(conversation)


@router.put("/conversations/{conversation_id}/name", response_model=ConversationPublic)
async def rename_conversation(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    session: SessionDep,
    async_session: SessionAsyncDep,
    request: ConversationRenameRequest,
) -> ConversationPublic:
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )
    conversation = await conversation_service.rename_conversation(
        conversation_id=conversation_id,
        name=request.name,
        workspace_id=current_user.current_workspace_id,
    )
    return ConversationPublic.model_validate(conversation)


@router.delete("/conversations/{conversation_id}", response_model=dict[str, str])
async def delete_conversation(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    session: SessionDep,
    async_session: SessionAsyncDep,
) -> dict[str, str]:
    """Delete a conversation and its associated LangGraph thread data."""
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )
    await conversation_service.delete_conversation(
        conversation_id, current_user.current_workspace_id
    )
    return {"status": "success"}


@router.get("/conversations", response_model=ConversationsPublic)
async def get_conversations(
    current_user: CurrentUser,
    session: SessionDep,
    async_session: SessionAsyncDep,
    agent_id: uuid.UUID | None = None,
    resource_id: uuid.UUID | None = None,
    search: str | None = None,
    skip: int = Query(
        default=0, ge=0, description="Number of records to skip for pagination"
    ),
    limit: int = Query(
        default=10, ge=1, le=100, description="Maximum number of records to return"
    ),
) -> ConversationsPublic:
    """Get list of conversations with filtering and pagination."""
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )
    conversations, total = await conversation_service.get_conversations(
        workspace_id=current_user.current_workspace_id,
        agent_id=agent_id,
        resource_id=resource_id,
        search=search,
        skip=skip,
        limit=limit,
    )
    return ConversationsPublic(
        data=[ConversationPublic.model_validate(conv) for conv in conversations],
        total=total,
    )


@router.get("/messages/{conversation_id}", response_model=MessagePublicList)
async def get_messages_history(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    session: SessionDep,
    async_session: SessionAsyncDep,
    skip: int = Query(
        default=0, ge=0, description="Number of records to skip for pagination"
    ),
    limit: int = Query(
        default=100, ge=1, le=200, description="Maximum number of records to return"
    ),
) -> MessagePublicList:
    conversation_service = ConversationService(
        session=session, async_session=async_session
    )
    return await conversation_service.get_conversation_history(
        conversation_id=conversation_id,
        workspace_id=current_user.current_workspace_id,
        skip=skip,
        limit=limit,
    )


@router.post("/chat/{conversation_id}/stream", response_model=StreamResponse)
async def chat_stream(
    current_user: CurrentUser,
    conversation_id: uuid.UUID,
    message: MessageStreamInput,
    session: SessionDep,
    async_session: SessionAsyncDep,
) -> StreamingResponse:
    response = await validator.validate_inputs(
        session,
        async_session,
        message,
        conversation_id,
        current_user.current_workspace_id,
        resource_id=uuid.UUID(message.resource_id) if message.resource_id else None,
    )
    conversation = response["conversation"]

    autonomous_agent_service = AgentService(session, async_session)

    async def event_generator():
        try:
            # Use enhanced process_message_stream that handles DB operations internally
            async for response in autonomous_agent_service.execute(
                ctx=ChatServiceContext(
                    # User context
                    user_id=current_user.id,
                    workspace_id=current_user.current_workspace_id,
                    conversation=conversation,
                    # Message context
                    user_prompt=message.content,
                    resume=message.resume,
                    approve=message.approve,
                    resource_id=uuid.UUID(message.resource_id)
                    if message.resource_id
                    else None,
                    # Runtime context
                    session=session,
                    async_session=async_session,
                    callbacks=[langfuse_handler],
                )
            ):
                if response["type"] == "error":
                    # Convert error responses to proper HTTP exceptions
                    error_content = response["content"]
                    if "not found" in error_content.lower():
                        raise HTTPException(status_code=404, detail=error_content)
                    elif "credentials" in error_content.lower():
                        raise HTTPException(status_code=403, detail=error_content)
                    else:
                        raise HTTPException(status_code=500, detail=error_content)
                else:
                    yield f"data: {json.dumps(response)}\n\n"

            yield f"data: {json.dumps({'type': 'complete'})}\n\n"

            # Trigger memory extraction in the background
            autonomous_agent_service.trigger_memory_extraction(conversation_id)
        except HTTPException as ex:
            # Re-raise HTTP exceptions as they already have the correct status code
            error_message = {
                "type": "error",
                "content": ex.detail,
                "status": ex.status_code,
            }
            logger.exception(f"HTTP error in stream: {ex.status_code} - {ex.detail}")
            yield f"data: {json.dumps(error_message)}\n\n"
            yield f"data: {json.dumps({'type': 'complete'})}\n\n"
            return
        except Exception as e:
            # Catch all other exceptions and return a 500 error
            error_message = {"type": "error", "content": str(e), "status": 500}
            logger.exception(f"Unexpected error in autonomous agent stream: {str(e)}")
            yield f"data: {json.dumps(error_message)}\n\n"
            yield f"data: {json.dumps({'type': 'complete'})}\n\n"
            return

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        },
    )
