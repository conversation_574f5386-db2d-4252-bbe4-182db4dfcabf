import uuid

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlmodel import Session
from sqlmodel.ext.asyncio.session import AsyncSession

from app.models import MessageStreamInput
from app.repositories.attachment import AttachmentRepository
from app.repositories.conversation import ConversationRepository
from app.services.agent.token_usage_handler import TokenUsageHandler


async def validate_inputs(
    session: Session,
    async_session: AsyncSession,
    input: MessageStreamInput,
    conversation_id: uuid.UUID,
    workspace_id: uuid.UUID,
    resource_id: uuid.UUID | None = None,
):
    # 1. Validate conversation exists
    conversation_repo = ConversationRepository(async_session=async_session)
    conversation = await conversation_repo.async_get_conversation(conversation_id)
    if not conversation:
        raise HTTPException(status_code=404, detail="Conversation not found")

    # 2. Validate attachment limits if attachments are provided
    if input.attachment_ids:
        attachment_repo = AttachmentRepository(async_session)
        is_valid, error_message = await attachment_repo.validate_attachment_limits(
            input.attachment_ids
        )
        if not is_valid:
            raise HTTPException(status_code=400, detail=error_message)

    # 3. Validate message content
    if not input.resume and not input.content:
        error_message = "Message content must be provided when not resuming"
        raise HTTPException(status_code=400, detail=error_message)

    # 4. Validate quota limits if not resuming
    if not input.resume:
        token_usage_handler = TokenUsageHandler(session, async_session)
        if await token_usage_handler.check_out_of_quota(workspace_id):
            raise HTTPException(status_code=429, detail="Out of quota")

    # 5.Attach resource to conversation if resource_id is provided
    if resource_id:
        await conversation_repo.attach_resource_to_conversation(
            conversation_id, resource_id
        )
        # Refresh the conversation object to get the updated resource_id
        conversation = await conversation_repo.async_get_conversation(conversation_id)

    return {
        "conversation": conversation,
    }
