from collections.abc import AsyncGenerator
from typing import Any

from langgraph.types import Command

from app.core.config import settings
from app.logger import logger
from app.repositories.notification import NotificationRepository
from app.schemas.chat import (
    ChatServiceContext,
    NamespaceChangeInput,
    NamespaceChangeOutput,
)

from .base_service import AgentBaseService


class AgentService(AgentBaseService):
    def _prepare_graph_input(
        self,
        user_message_content,
        message_content,
        resume,
        approve,
    ):
        if resume:
            return Command(
                resume={"approve": approve, "approve_message": message_content}
            )
        else:
            return {"messages": user_message_content}

    async def execute(
        self,
        ctx: ChatServiceContext,
    ) -> AsyncGenerator[dict[str, Any], None]:
        graph, ctx = await self._prepare_execution_context(ctx)

        # Process stream variables
        response_text = ""
        response_text_pointer = 0
        full_message_text = ""

        # Tracking agent thought
        current_thought = None
        current_thought_idx = 0

        # Tracking checkpoint ids
        first_checkpoint_id, last_checkpoint_id = None, None

        # Tracking namespace
        current_namespace = None

        # Tracking token usage
        input_token_usage, output_token_usage = 0, 0

        # if interrupted, namespace is last response namespace
        user_message_db = ctx.get_user_message_db()
        if ctx.resume:
            # Yield message ID at the start of the stream
            yield {
                "type": "message_id",
                "message_id": str(user_message_db.id),
                "namespace": user_message_db.role,
            }

            # Must account the cases where the latest response is display component
            if len(user_message_db.thoughts) > 0:
                thought_position = user_message_db.thoughts[-1].position
            else:
                thought_position = -1

            if len(user_message_db.display_components) > 0:
                display_component_position = user_message_db.display_components[
                    -1
                ].position
            else:
                display_component_position = -1

            if thought_position == -1 and display_component_position == -1:
                current_thought = None
                current_thought_idx = 0
            elif display_component_position > thought_position:
                current_thought = None
                current_thought_idx = display_component_position
            else:
                current_thought = user_message_db.thoughts[-1]
                current_thought_idx = thought_position

            # Mark notification as read
            repo = NotificationRepository()
            repo.sync_mark_notification_read_given_message_id(
                message_id=user_message_db.id,
                session=self.session,
            )

        # Prepare graph input
        graph_input = self._prepare_graph_input(
            ctx.user_prompt, ctx.user_prompt, ctx.resume, ctx.approve
        )

        # Start tracking token usage for the response message
        self.token_usage_handler.track_token_usage(
            message_id=str(user_message_db.id),
            input_token_count=0,
            output_token_count=0,
            model_id=settings.MAIN_MODEL_ID,
            workspace_id=str(ctx.workspace_id),
        )

        try:
            async for event in graph.astream_events(
                input=graph_input,
                config=ctx.to_dict(),
                stream_mode=["debug", "events", "custom"],
                version="v2",
                subgraphs=True,
            ):
                # if event["event"] == "on_chat_model_stream":
                #     continue

                # Token Usage Tracking
                input_token_usage, output_token_usage = (
                    self.stream_handler.handle_token_usage(
                        event, input_token_usage, output_token_usage
                    )
                )

                # Namespace Tracking
                if event["event"] == "on_chain_stream" and isinstance(
                    event["data"]["chunk"], tuple
                ):
                    event_data = event["data"]["chunk"][2]
                    event_type = event["data"]["chunk"][1]

                    if event_type == "custom":
                        if event_data["type"] == "on_agent_start":
                            namespace_change_output = self._handle_namespace_change(
                                NamespaceChangeInput(
                                    updated_namespace=event_data["content"],
                                    current_namespace=current_namespace,
                                    user_message_db=user_message_db,
                                    response_text=response_text,
                                    response_text_pointer=response_text_pointer,
                                    full_message_text=full_message_text,
                                    conversation_id=ctx.conversation.id,
                                    first_checkpoint_id=first_checkpoint_id,
                                    last_checkpoint_id=last_checkpoint_id,
                                    current_thought=current_thought,
                                    current_thought_idx=current_thought_idx,
                                    input_token_usage=input_token_usage,
                                    output_token_usage=output_token_usage,
                                )
                            )

                            if current_namespace != namespace_change_output.namespace:
                                if current_namespace:
                                    # Check if out of quota
                                    if await self.token_usage_handler.check_out_of_quota(
                                        ctx.workspace_id
                                    ):
                                        self.message_handler.cleanup_on_error(
                                            None, user_message_db
                                        )
                                        yield {
                                            "type": "error",
                                            "content": "Out of quota",
                                            "message_id": None,
                                        }
                                        return

                                    self.token_usage_handler.track_token_usage(
                                        message_id=str(
                                            namespace_change_output.response_message.id
                                        ),
                                        input_token_count=0,
                                        output_token_count=0,
                                        model_id=settings.MAIN_MODEL_ID,
                                        workspace_id=str(
                                            namespace_change_output.response_message.conversation.agent.workspace_id
                                        ),
                                    )

                                yield {
                                    "type": "message_id",
                                    "message_id": str(
                                        namespace_change_output.response_message.id
                                    ),
                                    "namespace": namespace_change_output.namespace,
                                }

                            current_namespace = namespace_change_output.namespace
                            user_message_db = namespace_change_output.response_message
                            response_text = namespace_change_output.response_text
                            response_text_pointer = (
                                namespace_change_output.response_text_pointer
                            )
                            full_message_text = (
                                namespace_change_output.full_message_text
                            )
                            current_thought = namespace_change_output.current_thought
                            first_checkpoint_id = (
                                namespace_change_output.first_checkpoint_id
                            )
                            last_checkpoint_id = (
                                namespace_change_output.last_checkpoint_id
                            )
                            current_thought_idx = (
                                namespace_change_output.current_thought_idx
                            )
                            input_token_usage = (
                                namespace_change_output.input_token_usage
                            )
                            output_token_usage = (
                                namespace_change_output.output_token_usage
                            )

                if event["event"] == "on_chain_stream" and isinstance(
                    event["data"]["chunk"], tuple
                ):
                    event_data = event["data"]["chunk"][2]
                    event_type = event["data"]["chunk"][1]

                    if event_type == "debug":
                        (
                            first_checkpoint_id,
                            last_checkpoint_id,
                            interrupt_result,
                        ) = self.stream_handler.handle_chain_stream_debug(
                            event_data,
                            first_checkpoint_id,
                            last_checkpoint_id,
                            user_message_db,
                            current_namespace,
                            ctx.resume,
                            full_message_text,
                        )

                        if interrupt_result:
                            interrupt_result["namespace"] = current_namespace
                            yield interrupt_result
                            user_message_db.role = (
                                current_namespace if current_namespace else "assistant"
                            )
                            self.session.add(user_message_db)
                            self.session.commit()

                            self.token_usage_handler.track_token_usage(
                                message_id=str(user_message_db.id),
                                input_token_count=input_token_usage,
                                output_token_count=output_token_usage,
                                model_id=settings.MAIN_MODEL_ID,
                                workspace_id=str(
                                    user_message_db.conversation.agent.workspace_id
                                ),
                            )
                            return
                        continue

                    elif event_type == "custom":
                        if (
                            event_data["type"]
                            == "on_recommendation_generation_response"
                        ):
                            # Increment thought index if needed
                            if current_thought:
                                current_thought_idx += 1

                            # Extract metadata for recommendations
                            additional_metadata = {}
                            if (
                                "metadata" in event_data
                                and "resource_id" in event_data["metadata"]
                            ):
                                additional_metadata["resource_id"] = event_data[
                                    "metadata"
                                ]["resource_id"]

                            # Save and stream recommendations in a single operation with metadata
                            components = self.component_handler.save_recommendations_as_components(
                                user_message_db,
                                event_data["content"],
                                current_thought_idx,
                                additional_metadata=additional_metadata,
                            )

                            # Reset state for next iteration
                            response_text = ""
                            response_text_pointer = 0
                            current_thought = None
                            current_thought_idx += 1

                            # Stream components efficiently
                            for component in components:
                                yield {
                                    "type": "display_component",
                                    "content": component,
                                    # "namespace": current_namespace,
                                }
                        elif (
                            event_type == "custom"
                            and event_data.get("type") == "on_chart_generation_response"
                        ):
                            # Handle custom chart component stream
                            chart_data = event_data.get("content")

                            if not chart_data:
                                logger.warning(
                                    "No chart data received in display component event"
                                )
                                continue

                            try:
                                # Increment current thought for display component
                                if current_thought:
                                    current_thought_idx += 1

                                chart_component, current_thought_idx = (
                                    self.component_handler.process_chart_component(
                                        response_message=user_message_db,
                                        chart_data=chart_data,
                                        component_position=current_thought_idx,
                                    )
                                )
                                response_text = ""
                                response_text_pointer = 0
                                current_thought = None
                                current_thought_idx += 1

                                # Format the component for streaming
                                if (
                                    isinstance(chart_data, dict)
                                    and "chart_data" in chart_data
                                ):
                                    if chart_component:
                                        # Format the component for streaming
                                        component_data = {
                                            "id": str(chart_component.id),
                                            "type": chart_component.type.value,
                                            "chart_type": chart_component.chart_type.value
                                            if chart_component.chart_type
                                            else None,
                                            "title": chart_component.title,
                                            "description": chart_component.description,
                                            "data": chart_component.data,
                                            "config": chart_component.config,
                                            "position": chart_component.position,
                                            "created_at": int(
                                                chart_component.created_at.timestamp()
                                            ),
                                        }

                                        # Stream the component
                                        yield {
                                            "type": "display_component",
                                            "content": component_data,
                                            # "namespace": current_namespace,
                                        }

                                else:
                                    logger.error("Invalid chart data format received")
                                    logger.error(f"Chart data: {chart_data}")
                            except Exception as e:
                                logger.exception(
                                    f"Error processing chart component: {str(e)}"
                                )
                                continue
                        elif (
                            event_type == "custom"
                            and event_data.get("type")
                            == "on_report_generation_response"
                        ):
                            report_data = event_data.get("content")
                            if report_data:
                                yield {
                                    "type": "on_report_generation_response",
                                    "content": report_data,
                                }
                        elif (
                            event_type == "custom"
                            and event_data.get("type")
                            == "on_dashboard_generation_response"
                        ):
                            dashboard_data = event_data.get("content")
                            if dashboard_data:
                                yield {
                                    "type": "on_dashboard_generation_response",
                                    "content": dashboard_data,
                                }
                        else:
                            yield event_data

                (
                    response_text,
                    response_text_pointer,
                    current_thought,
                    current_thought_idx,
                    result,
                ) = self.stream_handler.handle_stream_event(
                    event,
                    current_thought,
                    response_text,
                    response_text_pointer,
                    user_message_db,
                    current_thought_idx,
                )

                # Only yield result if it has a type and non-empty content
                if result["type"]:
                    if result["type"] == "stream":
                        if result.get("content") and result["content"].strip():
                            full_message_text += result["content"]
                            yield result
                    else:
                        yield result

            # Save final response
            if full_message_text:
                # For scheduling task
                yield {
                    "type": "final",
                    "namespace": current_namespace,
                }

                self.message_handler.save_final_response(
                    user_message_db,
                    full_message_text,
                    role=current_namespace if current_namespace else "assistant",
                )
                # Update end checkpoint after saving final response
                self.message_handler.update_message_checkpoint(
                    user_message_db, None, last_checkpoint_id
                )
                self.token_usage_handler.track_token_usage(
                    message_id=str(user_message_db.id),
                    input_token_count=input_token_usage,
                    output_token_count=output_token_usage,
                    model_id=settings.MAIN_MODEL_ID,
                    workspace_id=str(user_message_db.conversation.agent.workspace_id),
                )

        except Exception as e:
            logger.exception(f"Error processing message: {str(e)}")
            # Delete token usage for the message
            if await self.token_usage_handler.delete_token_usage(
                user_message_db.id, ctx.user_id
            ):
                logger.info(
                    f"Successfully deleted token usage for message {user_message_db.id}"
                )
            else:
                logger.error(
                    f"Failed to delete token usage for message {user_message_db.id}"
                )
            self.message_handler.cleanup_on_error(user_message_db, ctx.user_message_db)

            yield {"type": "error", "content": str(e), "message_id": None}

    async def execute_background_task(self, ctx: ChatServiceContext) -> dict:
        final_content = {}

        try:
            # Process the message using the stream method
            event_stream = self.execute(ctx=ctx)

            # Process stream events
            async def collect_final():
                async for event in event_stream:
                    if (
                        event.get("type") == "final"
                        or event.get("type") == "interrupt"
                        or event.get("type") == "error"
                    ):
                        final_content = event

                return final_content

            # Run async collection synchronously
            final_content = await collect_final()

            return final_content

        except Exception as e:
            logger.exception(f"Error processing message: {str(e)}")
            raise

    def _handle_namespace_change(
        self, input: NamespaceChangeInput
    ) -> NamespaceChangeOutput:
        """Handle namespace changes in the event stream.

        Args:
            input (NamespaceChangeInput): Input schema for namespace change event

        Returns:
            NamespaceChangeOutput: Output schema for namespace change event
        """
        if input.current_namespace is None and input.updated_namespace:
            self.message_handler.update_message_namespace(
                input.user_message_db.id, input.updated_namespace
            )

        if (
            input.current_namespace is not None
            and input.updated_namespace != input.current_namespace
        ):
            self.message_handler.save_final_response(
                input.user_message_db, input.full_message_text, input.current_namespace
            )
            self.message_handler.update_message_checkpoint(
                input.user_message_db, None, input.last_checkpoint_id
            )
            self.token_usage_handler.track_token_usage(
                message_id=str(input.user_message_db.id),
                input_token_count=input.input_token_usage,
                output_token_count=input.output_token_usage,
                model_id=settings.MAIN_MODEL_ID,
                workspace_id=str(input.user_message_db.conversation.agent.workspace_id),
            )
            input_token_usage, output_token_usage = 0, 0

            response_message = self.message_handler.create_message(
                input.conversation_id, "", input.updated_namespace
            )
            return NamespaceChangeOutput(
                namespace=input.updated_namespace,
                response_message=response_message,
                response_text="",
                response_text_pointer=0,
                full_message_text="",
                current_thought=None,
                first_checkpoint_id=None,
                last_checkpoint_id=None,
                current_thought_idx=0,
                input_token_usage=input_token_usage,
                output_token_usage=output_token_usage,
            )

        return NamespaceChangeOutput(
            namespace=input.updated_namespace,
            response_message=input.user_message_db,
            response_text=input.response_text,
            response_text_pointer=input.response_text_pointer,
            full_message_text=input.full_message_text,
            current_thought=input.current_thought,
            first_checkpoint_id=input.first_checkpoint_id,
            last_checkpoint_id=input.last_checkpoint_id,
            current_thought_idx=input.current_thought_idx,
            input_token_usage=input.input_token_usage,
            output_token_usage=input.output_token_usage,
        )
