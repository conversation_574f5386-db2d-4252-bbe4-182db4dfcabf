'use client';

import { useEffect } from 'react';

import { TabAutonomousChatContainer } from '@/components/chat/autonomous/chat-panel';
import {
  conversationQuery,
  useConversationCreator,
} from '@/features/conversation';
import { convertToSession } from '@/lib/message-converters';
import { Session } from '@/types/chat';

import { ChatProvider } from '@/features/chat/context/chat-context';
import { useAutonomousChat } from '@/features/chat/hooks/use-autonomous-chat';
import { useInitialMessageHandler } from '@/features/chat/hooks/use-initial-message-handler';
import { useUrlManager } from '@/features/chat/hooks/use-url-manager';

// Utility function to create session from conversation
const createSessionFromConversation = (
  conversationId: string,
  conversations: Session[],
): Session | undefined => {
  // Find the current conversation
  const conversation = conversations.find((c) => c.id === conversationId);
  if (conversation) {
    return conversation;
  }

  // If not found and we have a conversationId, create a minimal session
  if (conversationId) {
    return {
      id: conversationId,
      title: 'Conversation',
      timestamp: new Date(),
    };
  }

  return undefined;
};

interface ViewPageProps {
  id: string;
  conversationId?: string;
  initialMessage?: string;
  resourceId?: string;
}

export default function ViewPage({
  id,
  conversationId,
  initialMessage,
  resourceId,
}: ViewPageProps) {
  // Core chat functionality
  const chat = useAutonomousChat({ conversationId });

  // Conversation management using new system
  const conversationsQuery = conversationQuery.query.useByAgent(id, resourceId);
  const { createOnly, isCreating } = useConversationCreator();
  const renameMutation = conversationQuery.mutation.useRename(
    chat.selectedConversation || '',
  );
  const deleteMutation = conversationQuery.mutation.useDelete(
    chat.selectedConversation || '',
  );

  // Convert API conversations to Session format for backward compatibility
  const conversations: Session[] =
    conversationsQuery.data?.data?.map(convertToSession) || [];

  // Conversation management functions
  const createConversation = async () => {
    if (isCreating) return;

    try {
      const conversation = await createOnly({
        agentId: id,
        resourceId,
        onSuccess: chat.setSelectedConversation,
      });

      return conversation;
    } catch (_error) {
      console.error('Failed to create conversation:', _error);
    }
  };

  const renameConversation = async (_conversationId: string, name: string) => {
    try {
      await renameMutation.mutateAsync({ name });
    } catch {
      // Error handling is done in the mutation
    }
  };

  const deleteConversation = async (conversationId: string) => {
    try {
      await deleteMutation.mutateAsync();

      // Handle conversation deletion callback
      if (chat.selectedConversation === conversationId) {
        chat.setSelectedConversation(null);
      }
    } catch {
      // Error handling is done in the mutation
    }
  };

  // Create conversationManager-like object for backward compatibility
  const conversationManager = {
    conversations,
    isLoadingConversations: conversationsQuery.isLoading,
    isCreatingConversation: isCreating,
    createConversation,
    renameConversation,
    deleteConversation,
  };

  // URL management
  useUrlManager({
    selectedConversation: chat.selectedConversation,
    conversationId,
    mounted: chat.mounted,
    setSelectedConversation: chat.setSelectedConversation,
    setInterruptConfirmation: chat.setInterruptConfirmation,
  });

  // Initial message handling
  useInitialMessageHandler({
    mounted: chat.mounted,
    initialMessage,
    initialMessageSent: chat.initialMessageSent,
    selectedConversation: chat.selectedConversation,
    createConversationPending: conversationManager.isCreatingConversation,
    conversationId,
    handleSendMessage: chat.handleSendMessage,
    setInitialMessageSent: chat.setInitialMessageSent,
    handleCreateConversation: conversationManager.createConversation,
  });

  // Mount effect
  useEffect(() => {
    chat.setMounted(true);
  }, [chat.setMounted]);

  // Don't render until mounted
  if (!chat.mounted) return null;

  // Create session from conversation
  const currentSession = createSessionFromConversation(
    chat.selectedConversation || '',
    conversationManager.conversations,
  );

  return (
    <ChatProvider
      value={{
        messages: chat.streamingMessages || [],
        onSendMessage: chat.handleSendMessage,
        isStreaming: chat.isStreaming,
        currentSession,
        onNewChat: conversationManager.createConversation,
        onStopStreaming: chat.stopStream,
        confirmation: chat.interruptConfirmation,
        chatType: resourceId ? 'resource' : 'agent',
        resourceId,
        isCreatingConversation: conversationManager.isCreatingConversation,
        currentReport: chat.currentReport,
        currentDashboard: chat.currentDashboard,
        conversationId: chat.selectedConversation || undefined,
        thinkingContent: chat.thinkingContent,
        planningContent: chat.planningContent,
        conversationResourceId: chat.conversationResourceId,
        hasReport: chat.hasReport,
        hasDashboard: chat.hasDashboard,
      }}
    >
      <TabAutonomousChatContainer />
    </ChatProvider>
  );
}
