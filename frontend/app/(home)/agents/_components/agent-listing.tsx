'use client';

import Link from 'next/link';

import { AgentCard } from '@/components/agents/agent-card';
import { LoadingSkeleton } from '@/components/agents/loading-skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import pathsConfig from '@/config/paths.config';
import { AgentCardList } from '@/features/agent/components/agent-card-list';
import { agentQuery } from '@/features/agent/hooks/agent.query';
import { AgentProvider } from '@/features/agent/provider/agent-provider';
import { AgentType } from '@/openapi-ts/gens';

const AgentListingPage = () => {
  const { data: agentsResponse, isLoading } = agentQuery.query.useList();

  if (agentsResponse) {
    const autonomousAgents = agentsResponse.data.filter(
      (agent) => agent.type === AgentType.autonomous_agent,
    );
    const conversationalAgents = agentsResponse.data.filter(
      (agent) => agent.type === AgentType.conversation_agent,
    );

    return (
      <AgentProvider>
        <div className="flex flex-1 flex-col gap-4">
          {autonomousAgents.length > 0 && (
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-semibold tracking-tight">
                  Group Chat
                </h2>
              </div>
              <ScrollArea className="h-auto">
                <div className="grid grid-cols-1 gap-4 pr-4 sm:grid-cols-2 lg:grid-cols-3">
                  {autonomousAgents.map((item) => (
                    <Link
                      key={item.id}
                      href={pathsConfig.app.agentDetail(item.id)}
                      className="hover:[&>*]:border-primary hover:[&>*]:bg-accent transition-all"
                    >
                      <AgentCard item={item} />
                    </Link>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {conversationalAgents.length > 0 && (
            <div className="flex min-h-0 flex-1 flex-col gap-2">
              <div className="flex items-center gap-2">
                <h2 className="text-xl font-semibold tracking-tight">
                  Team Members
                </h2>
              </div>
              <div className="min-h-0 flex-1">
                <ScrollArea className="h-full">
                  <AgentCardList />
                </ScrollArea>
              </div>
            </div>
          )}
        </div>
      </AgentProvider>
    );
  }

  if (isLoading) {
    return <LoadingSkeleton />;
  }
};

export default AgentListingPage;
