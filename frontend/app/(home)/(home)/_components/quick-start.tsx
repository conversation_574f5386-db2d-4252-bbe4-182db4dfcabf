'use client';

import { useMemo, useRef, useState } from 'react';

import { TaskTemplate } from '@/client';
import {
  AutocompleteDropdown,
  AutocompleteSuggestion,
} from '@/components/chat/components/autocomplete-dropdown';
import { Button } from '@/components/ui/button';
import { Prompt } from '@/features/agent/components/prompt-card';
import { useDebounce } from '@/hooks/use-debounce';
import { useTaskTemplates } from '@/hooks/use-task-templates';
import { useConversationCreator } from '@/features/conversation';
import { ArrowDown, LucideIcon } from 'lucide-react';
import { useInView } from 'react-intersection-observer';

import { ExamplePrompts } from './example-prompts';
import { HeroHeading } from './hero-heading';
import { Hub, QuickStartCardType } from './hub';
import { QuickGenerate, QuickGenerateRef } from './quick-generate';
import { QuickJumpDialog } from './quick-jump';

export interface RecentTask extends QuickStartCardType {
  timestamp: number;
  type: 'generated' | 'direct';
}

interface ExtendedAutocompleteSuggestion extends AutocompleteSuggestion {
  isLoading?: boolean;
  isEmpty?: boolean;
  category?: string;
}

export default function QuickStart() {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<{
    title: string;
    description: string;
    category?: string;
    isGenerated?: boolean;
    service?: string;
    icon?: LucideIcon;
  } | null>(null);

  const { createAndNavigate } = useConversationCreator();
  const { ref: inViewRef, inView: isHubInView } = useInView({ threshold: 0.1 });
  const [showAutocomplete, setShowAutocomplete] = useState(false);

  const [autocompleteFilter, setAutocompleteFilter] = useState('');
  const debouncedAutocompleteFilter = useDebounce(autocompleteFilter, 300);

  // Add ref for QuickGenerate
  const quickGenerateRef = useRef<QuickGenerateRef>(null);

  // Updated task templates query with debounced filter
  const { data: templatesData, isPending: isTemplatesLoading } =
    useTaskTemplates({
      limit: 5,
      searchQuery: debouncedAutocompleteFilter,
    });

  // Enhanced autocomplete suggestions with loading and empty states
  const suggestions: ExtendedAutocompleteSuggestion[] = useMemo(() => {
    return (
      templatesData?.data.map((template: TaskTemplate) => ({
        id: template.id,
        title: template.task,
        description: template.context || '',
        category: template.category,
      })) || []
    );
  }, [templatesData]);

  const handleCardClick = (card: QuickStartCardType) => {
    setSelectedTask({
      title: card.title,
      description: card.description,
      category: card.category,
      service: card.service,
      icon: card.icon,
      isGenerated: false,
    });
    setIsDialogOpen(true);
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsDialogOpen(open);
    if (!open) {
      setTimeout(() => {
        if (!isDialogOpen) {
          setSelectedTask(null);
        }
      }, 300);
    }
  };

  const handleTaskGenerated = (task: {
    title: string;
    description: string;
    category?: string;
  }) => {
    setSelectedTask(task);
    setIsDialogOpen(true);
  };

  // Handle example prompt click
  const handleExamplePrompt = async (prompt: Prompt) => {
    await createAndNavigate({
      initialMessage: prompt.question,
      onSuccess: () => {
        // Any additional success handling specific to example prompts
      },
      onError: (error) => {
        // Any additional error handling specific to example prompts
        console.error('Example prompt error:', error);
      },
    });
  };

  const handleScrollToHub = () => {
    const hubSection = document.getElementById('hub-section');
    if (hubSection) {
      hubSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="from-background via-background/95 to-background/90 flex flex-1 flex-col bg-linear-to-b">
      <div className="relative flex min-h-dvh flex-col">
        <HeroHeading />

        <div className="flex flex-col justify-center">
          <div className="w-full px-2">
            <QuickGenerate
              ref={quickGenerateRef}
              setShowAutocomplete={setShowAutocomplete}
              setAutocompleteFilter={setAutocompleteFilter}
              showGitHubButton={false}
              setSelectedTask={handleTaskGenerated}
              setIsDialogOpen={setIsDialogOpen}
              messagesRemaining={0}
              className="rounded-lg shadow-none transition-all duration-200 hover:shadow-none"
            />
            <div className="bg-primary/10 pointer-events-none absolute inset-0 -z-10 rounded-lg blur-lg" />
          </div>

          {/* Example Prompts Section */}
          <div className="px-6">
            {showAutocomplete ? (
              <AutocompleteDropdown
                isLoading={isTemplatesLoading}
                isVisible={showAutocomplete}
                suggestions={suggestions as AutocompleteSuggestion[]}
                onSelect={(suggestion) => {
                  quickGenerateRef.current?.handleAutocompleteSelect(
                    suggestion as ExtendedAutocompleteSuggestion,
                  );
                }}
                onClose={() => setShowAutocomplete(false)}
              />
            ) : (
              <ExamplePrompts onPromptClick={handleExamplePrompt} />
            )}
          </div>
        </div>

        {/* Scroll to Hub Button */}
        {!isHubInView && (
          <div className="pointer-events-none absolute right-0 bottom-4 left-0 z-50 flex justify-center">
            <Button
              variant="outline"
              size="sm"
              className="border-primary/20 bg-background/80 text-primary hover:border-primary/40 hover:bg-primary/10 pointer-events-auto animate-bounce rounded-lg border-2 shadow-lg backdrop-blur-xs"
              onClick={handleScrollToHub}
            >
              <ArrowDown className="mr-2 h-4 w-4" />
              Hub Section
            </Button>
          </div>
        )}
      </div>

      {/* Hub Section */}
      <div ref={inViewRef} id="hub-section">
        <Hub onCardClick={handleCardClick} isLoading={false} />
      </div>

      {/* Dialog */}
      {selectedTask && (
        <QuickJumpDialog
          open={isDialogOpen}
          onOpenChange={handleDialogOpenChange}
          initialMessage={selectedTask.description}
          title={selectedTask.title}
          task_title={selectedTask.title}
          isGenerated={selectedTask.isGenerated}
        />
      )}
    </div>
  );
}
