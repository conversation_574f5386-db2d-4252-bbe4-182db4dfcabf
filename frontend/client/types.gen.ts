// This file is auto-generated by @hey-api/openapi-ts

export type AccountEnvironement = 'production' | 'staging' | 'development';

export type ActivationResponse = {
    message: string;
    expires_at: string;
};

export type ActivationResult = {
    success: boolean;
    message: string;
    redirect_url?: (string | null);
    welcome_message?: (string | null);
};

export type Address = {
    city?: (string | null);
    country?: (string | null);
    line1?: (string | null);
    line2?: (string | null);
    postal_code?: (string | null);
    state?: (string | null);
};

export type AgentBuiltInToolBulkUpdateResult = {
    workspace_builtin_tool_id: string;
    success: boolean;
    error_message?: (string | null);
};

export type AgentBuiltInToolPublic = {
    id: string;
    name: string;
    display_name: string;
    description?: (string | null);
    required_permission: boolean;
    is_active: boolean;
};

export type AgentBuiltInToolsBulkUpdateResponse = {
    total_count: number;
    success_count: number;
    failed_count: number;
    results: Array<AgentBuiltInToolBulkUpdateResult>;
};

export type AgentBuiltInToolsPublic = {
    agent_id: string;
    tools: Array<AgentBuiltInToolPublic>;
};

export type AgentBuiltInToolsUpdateRequest = {
    agent_builtin_tools: Array<AgentBuiltInToolUpdate>;
};

export type AgentBuiltInToolUpdate = {
    workspace_builtin_tool_id: string;
    is_active: boolean;
};

/**
 * Request model for creating agent connection
 */
export type AgentConnectionCreateRequest = {
    connection_id: string;
};

/**
 * Response model for single operations
 */
export type AgentConnectionResponse = {
    success: boolean;
    message: string;
};

/**
 * Public schema for agent connections
 */
export type AgentConnectionsPublic = {
    agent_id: string;
    connections: Array<ConnectionPublic>;
};

/**
 * Configuration for agent execution
 */
export type AgentExecutionConfig = {
    message: string;
    agent_id: string;
    context_ids?: Array<(string)>;
    conversation_name?: (string | null);
};

export type AgentInstructionsUpdate = {
    instructions: string;
};

export type AgentPublic = {
    id: string;
    alias: string;
    title: string;
    role: string;
    goal: string;
    instructions: string;
    is_active: boolean;
    type: AgentType;
};

export type AgentsBuiltInToolsResponse = {
    agents_builtin_tools: Array<AgentBuiltInToolsPublic>;
};

/**
 * Response model for multiple agents' connections
 */
export type AgentsConnectionsResponse = {
    agents_connections: Array<AgentConnectionsPublic>;
};

export type AgentsPublic = {
    data: Array<AgentPublic>;
    count: number;
};

export type AgentStatusUpdate = {
    agent_status: boolean;
};

/**
 * Defines the supported types of agents in the system.
 */
export type AgentType = 'conversation_agent' | 'autonomous_agent';

export type AgentTypeUsage = {
    agent_type: string;
    total_tokens: number;
};

/**
 * Schema for creating a new alert
 */
export type AlertCreate = {
    /**
     * Alert title
     */
    title: string;
    /**
     * Detailed alert description
     */
    description: string;
    /**
     * Alert severity level
     */
    severity: AlertSeverity;
};

/**
 * Schema for list of alerts with pagination
 */
export type AlertList = {
    data: Array<AlertResponse>;
    total: number;
};

/**
 * Schema for alert response
 */
export type AlertResponse = {
    /**
     * Alert title
     */
    title: string;
    /**
     * Detailed alert description
     */
    description: string;
    /**
     * Alert severity level
     */
    severity: AlertSeverity;
    id: string;
    workspace_id: string;
    status: AlertStatus;
    created_at: string;
    updated_at?: (string | null);
};

export type AlertSeverity = 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' | 'INFO';

export type AlertStatus = 'OPEN' | 'ACKNOWLEDGED' | 'RESOLVED' | 'CLOSED';

/**
 * Summary of alerts by status for the last 30 days
 */
export type AlertStatusSummary = {
    /**
     * Count of alerts by status
     */
    status_counts: {
        [key: string]: (number);
    };
    /**
     * Total number of alerts in the period
     */
    total: number;
};

/**
 * Schema for updating an existing alert
 */
export type AlertUpdate = {
    title?: (string | null);
    description?: (string | null);
    severity?: (AlertSeverity | null);
    status?: (AlertStatus | null);
};

/**
 * Schema for returning all constants at once.
 */
export type AllConstantsResponse = {
    constants: {
        [key: string]: ConstantCategory;
    };
    total_categories: number;
    generated_at: string;
};

/**
 * Information about a generated presigned URL
 */
export type app__schemas__kb__PresignedUrlInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * Storage key for the file
     */
    storage_key: string;
    /**
     * Presigned URL for file upload
     */
    presigned_url: string;
};

/**
 * Response schema for task status operations
 */
export type app__schemas__kb__TaskStatusResponse = {
    /**
     * Celery task ID
     */
    task_id: string;
    /**
     * Task status (PENDING, PROGRESS, SUCCESS, FAILURE)
     */
    status: AsyncTaskStatus;
    /**
     * Progress percentage (0-100)
     */
    progress?: number;
    /**
     * Task result if completed
     */
    result?: ({
    [key: string]: unknown;
} | null);
    /**
     * Error message if failed
     */
    error?: (string | null);
    /**
     * Human-readable status message
     */
    status_message?: (string | null);
};

export type app__schemas__message_attachment__PresignedUrlInfo = {
    /**
     * Client-side unique ID for the file.
     */
    file_id: string;
    /**
     * Sanitized name of the file.
     */
    filename: string;
    /**
     * The key for the object in storage.
     */
    storage_key: string;
    /**
     * The presigned URL for uploading.
     */
    presigned_url: string;
};

export type app__schemas__message_attachment__TaskStatusResponse = {
    task_id: string;
    status: AsyncTaskStatus;
    status_message?: (string | null);
    progress?: number;
    result?: ({
    [key: string]: unknown;
} | null);
    error?: (string | null);
};

export type app__schemas__onboarding__TaskTemplateResponse = {
    cloud_provider: string;
    templates: Array<TaskTemplate>;
};

export type app__schemas__task_template__TaskTemplateResponse = {
    id: string;
    task: string;
    category: TaskCategoryEnum;
    service: TaskServiceEnum;
    service_name: string;
    cloud: TaskCouldEnum;
    run_mode: RunModeEnum;
    schedule: (string | null);
    context: string;
    is_default: boolean;
    created_at: string;
    updated_at: (string | null);
};

export type AsyncTaskStatus = 'PENDING' | 'PROGRESS' | 'SUCCESS' | 'FAILURE';

export type AttachmentConfirmRequest = {
    /**
     * List of files that have been successfully uploaded.
     */
    uploaded_files: Array<UploadedAttachmentInfo>;
};

export type AttachmentDownloadResponse = {
    /**
     * The ID of the attachment.
     */
    attachment_id: string;
    /**
     * The presigned URL for downloading the file.
     */
    download_url: string;
};

export type AttachmentFileInfo = {
    /**
     * Client-side unique ID for the file.
     */
    file_id: string;
    /**
     * Original name of the file.
     */
    filename: string;
    /**
     * MIME type of the file.
     */
    content_type: string;
    /**
     * Size of the file in bytes.
     */
    file_size: number;
};

export type AttachmentMetadataResponse = {
    /**
     * The ID of the attachment.
     */
    id: string;
    /**
     * The name of the file.
     */
    filename: string;
    /**
     * The original name of the file.
     */
    original_filename: string;
    /**
     * The MIME type of the file.
     */
    file_type: string;
    /**
     * The size of the file in bytes.
     */
    file_size: number;
    /**
     * The storage key of the file.
     */
    storage_key: string;
    /**
     * The creation timestamp of the attachment.
     */
    created_at: string;
};

export type AttachmentPresignedUrlRequest = {
    /**
     * List of files to generate presigned URLs for.
     */
    files: Array<AttachmentFileInfo>;
};

export type AttachmentPresignedUrlResponse = {
    /**
     * List of presigned URLs and associated file info.
     */
    presigned_urls: Array<app__schemas__message_attachment__PresignedUrlInfo>;
};

export type AvailableUser = {
    id: string;
    email: string;
    full_name: string;
};

export type AvailableUsersCurrentWorkspace = {
    data: Array<AvailableUser>;
    count: number;
};

export type AWSAccountCreate = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    account_id: string;
    access_key_id: string;
    secret_access_key: string;
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_pattern: string;
};

export type AWSAccountDetail = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    id: string;
    access_key_id: string;
    secret_access_key: string;
    account_id: string;
};

export type AWSAccountPublic = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    id: string;
};

export type AWSAccountUpdate = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    account_id?: (string | null);
    access_key_id?: (string | null);
    secret_access_key?: (string | null);
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_pattern: string;
};

export type AWSOnboardingCreate = {
    aws_access_key_id: string;
    aws_secret_access_key: string;
    aws_default_region: string;
};

export type AzureOnboardingCreate = {
    app_id: string;
    client_secret: string;
    tenant: string;
};

export type BillingDetails = {
    address: Address;
    email?: (string | null);
    name?: (string | null);
    phone?: (string | null);
};

export type Body_login_login_access_token = {
    grant_type?: string;
    username: string;
    password: string;
    scope?: string;
    client_id?: (string | null);
    client_secret?: (string | null);
    slackOAuth?: boolean;
    appId?: (string | null);
    teamId?: (string | null);
};

export type Body_notifications_list_notifications = {
    type?: (Array<NotificationType> | null);
    status?: (Array<NotificationStatus> | null);
};

/**
 * Request model for installing builtin connections with custom configuration
 */
export type BuiltinInstallRequest = {
    /**
     * Configuration override for the builtin connection (e.g., environment variables)
     */
    config_override?: ({
    [key: string]: unknown;
} | null);
};

export type BuiltInToolPublic = {
    name: string;
    display_name: string;
    description: string;
};

export type BuiltInToolUpdateRequest = {
    required_permission: boolean;
};

export type CardDetails = {
    brand: string;
    country: string;
    display_brand: string;
    exp_month: number;
    exp_year: number;
    last4: string;
};

/**
 * Enum for different types of charts available in the system
 */
export type ChartType = 'line' | 'bar' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'radar' | 'step_area' | 'sankey';

export type CheckoutSessionResponse = {
    checkout_session_url: string;
};

export type CloudProvider = 'AWS' | 'GCP' | 'AZURE';

export type CloudSyncConfigCreate = {
    include_stopped_resources?: boolean;
    refresh_interval?: number;
    selected_resources?: Array<(string)>;
    is_enabled?: boolean;
    connection_id: string;
};

export type CloudSyncConfigPublic = {
    include_stopped_resources?: boolean;
    refresh_interval?: number;
    selected_resources?: Array<(string)>;
    is_enabled?: boolean;
    id: string;
    workspace_id: string;
    connection_id: string;
    created_at: string;
    updated_at: string;
    last_sync_at?: (string | null);
};

export type CloudSyncConfigUpdate = {
    include_stopped_resources?: (boolean | null);
    refresh_interval?: (number | null);
    selected_resources?: (Array<(string)> | null);
    is_enabled?: (boolean | null);
};

/**
 * Request to confirm file uploads and start ingestion
 */
export type ConfirmUploadsRequest = {
    /**
     * Information about successfully uploaded files
     */
    uploaded_files: Array<UploadedFileInfo>;
};

export type ConnectionCreate = {
    name: string;
    prefix: string;
    type?: ConnectionType;
    transport_type?: ConnectionTransport;
    config?: {
        [key: string]: unknown;
    };
    is_active?: boolean;
    tool_list?: Array<(string)>;
    tool_permissions?: Array<(string)>;
    tool_enabled?: Array<(string)>;
    tool_schemas?: Array<{
        [key: string]: unknown;
    }>;
    status?: ConnectionStatus;
    status_message?: string;
    status_updated_at?: string;
};

export type ConnectionPublic = {
    name: string;
    prefix: string;
    type: ConnectionType;
    transport_type: ConnectionTransport;
    config: {
        [key: string]: unknown;
    };
    is_active: boolean;
    tool_list: Array<(string)>;
    tool_permissions: Array<(string)>;
    tool_enabled: Array<(string)>;
    status: ConnectionStatus;
    status_message: string;
    status_updated_at: string;
    id: string;
    workspace_id: (string | null);
    created_at: string;
    updated_at: string;
};

export type ConnectionsPublic = {
    data: Array<ConnectionPublic>;
    count: number;
};

export type ConnectionStatus = 'connected' | 'error';

export type ConnectionTransport = 'streamable_http' | 'sse';

export type ConnectionType = 'builtin' | 'mcp' | 'cloud' | 'cli';

export type ConnectionUpdate = {
    name?: (string | null);
    prefix?: (string | null);
    type?: (ConnectionType | null);
    transport_type?: (ConnectionTransport | null);
    config?: ({
    [key: string]: unknown;
} | null);
    is_active?: (boolean | null);
    tool_list?: (Array<(string)> | null);
    tool_permissions?: (Array<(string)> | null);
    tool_enabled?: (Array<(string)> | null);
    tool_schemas?: (Array<{
    [key: string]: unknown;
}> | null);
    status?: (ConnectionStatus | null);
    status_message?: (string | null);
    status_updated_at?: (string | null);
};

/**
 * Schema for listing all available constant categories.
 */
export type ConstantCategoriesResponse = {
    categories: Array<(string)>;
    total: number;
};

/**
 * Schema for a constant category response.
 */
export type ConstantCategory = {
    category: string;
    data: Array<ConstantOption>;
    description: string;
    total: number;
    last_updated: string;
};

/**
 * Schema for a single constant option.
 */
export type ConstantOption = {
    value: string;
    label: string;
    key: string;
    metadata?: ({
    [key: string]: unknown;
} | null);
};

export type ConversationCreateRequest = {
    agent_id: string;
    resource_id?: (string | null);
};

export type ConversationPublic = {
    id: string;
    agent_id: string;
    name: string;
    resource?: (ResourcePublicSimple | null);
    created_at: string;
};

export type ConversationRenameRequest = {
    name: string;
};

/**
 * Response model for paginated conversations list.
 */
export type ConversationsPublic = {
    data: Array<ConversationPublic>;
    total: number;
};

export type DailyMessageVolume = {
    date: string;
    message_count: number;
};

export type DailyTokenUsage = {
    date: string;
    total_tokens: number;
};

export type Dashboard = {
    id?: string;
    conversation_id: string;
    workspace_id: string;
    title: string;
    grid_config?: {
        [key: string]: unknown;
    };
    widgets?: Array<unknown>;
    created_at?: string;
    updated_at?: string;
};

export type DocumentKBRead = {
    name: string;
    type: DocumentType;
    url?: (string | null);
    deep_crawl?: boolean;
    file_name?: (string | null);
    file_type?: (string | null);
    object_name?: (string | null);
    embed_status?: AsyncTaskStatus;
    id: string;
    kb_id: string;
    created_at: string;
    updated_at: string;
    is_deleted: boolean;
    parent_id?: (string | null);
    children?: Array<DocumentKBRead>;
};

export type DocumentsKBRead = {
    data: Array<DocumentKBRead>;
    count: number;
};

export type DocumentType = 'url' | 'file';

/**
 * Response model for enterprise enquiry status messages
 */
export type EnterpriseEnquiryMessageResponse = {
    message: string;
};

export type EnterpriseEnquiryRequest = {
    first_name: string;
    last_name: string;
    work_title: string;
    work_email: string;
    company_name: string;
    estimated_monthly_cost: string;
    message: string;
    product_id: string;
};

/**
 * Enumeration for feedback types on agent responses.
 */
export type FeedbackType = 'good' | 'bad';

/**
 * Response for file content requests
 */
export type FileContentResponse = {
    /**
     * File content
     */
    content: string;
    /**
     * File path
     */
    path: string;
    /**
     * File name
     */
    name: string;
};

/**
 * Information about a file to generate a presigned URL for
 */
export type FileInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * File MIME type
     */
    content_type: string;
    /**
     * File size in bytes
     */
    file_size: number;
};

/**
 * Response for directory listing
 */
export type FileListResponse = {
    /**
     * List of files and directories
     */
    files: Array<FileNode>;
    /**
     * Current directory path
     */
    current_path: string;
};

/**
 * Simplified file node matching frontend interface
 */
export type FileNode = {
    /**
     * Unique identifier for the file/directory
     */
    id: string;
    /**
     * File or directory name
     */
    name: string;
    /**
     * Full path
     */
    path: string;
    /**
     * File type (file or directory)
     */
    type: FileType;
    /**
     * Child nodes for directories
     */
    children?: (Array<FileNode> | null);
    /**
     * File content for files
     */
    content?: (string | null);
};

export type FileType = 'file' | 'directory';

export type GCPAccountCreate = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    key: string;
};

export type GCPAccountDetail = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    id: string;
    key: string;
};

export type GCPAccountPublic = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    id: string;
};

export type GCPAccountUpdate = {
    name: string;
    description?: (string | null);
    environment: AccountEnvironement;
    workspace_id: string;
    key: string;
};

export type GCPOnboardingCreate = {
    google_service_account_key: string;
    provider_id: string;
};

export type HTTPValidationError = {
    detail?: Array<ValidationError>;
};

export type InvoiceResponse = {
    id: string;
    customer: string;
    status: string;
    amount_due: number;
    amount_paid: number;
    amount_remaining: number;
    currency: string;
    invoice_pdf?: (string | null);
    created: number;
    due_date?: (number | null);
    paid: boolean;
    payment_intent?: (string | null);
    subscription?: (string | null);
    total: number;
};

export type KBAccessLevel = 'private' | 'shared';

export type KBCreate = {
    title: string;
    description?: (string | null);
    access_level?: KBAccessLevel;
    usage_mode?: KBUsageMode;
    tags?: Array<(string)>;
    allowed_users?: Array<(string)>;
};

export type KBRead = {
    title: string;
    description?: (string | null);
    access_level?: KBAccessLevel;
    usage_mode?: KBUsageMode;
    tags?: Array<(string)>;
    id: string;
    created_at: string;
    updated_at: string;
    is_deleted: boolean;
    allowed_users?: Array<(string)>;
    owner_id: string;
};

export type KBsRead = {
    data: Array<KBRead>;
    count: number;
};

export type KBUpdate = {
    title?: (string | null);
    description?: (string | null);
    access_level?: (KBAccessLevel | null);
    tags?: (Array<(string)> | null);
    allowed_users?: (Array<(string)> | null);
    usage_mode?: (KBUsageMode | null);
};

export type KBUsageMode = 'manual' | 'agent_requested' | 'always';

export type Message = {
    content: string;
    role?: string;
    is_interrupt?: boolean;
    interrupt_message?: (string | null);
    id?: string;
    conversation_id: string;
    created_at?: string;
    updated_at?: string;
    message_metadata?: {
        [key: string]: unknown;
    };
    is_deleted?: boolean;
};

export type MessageAgentThoughtPublic = {
    id: string;
    position: number;
    tool_name: string;
    tool_input: {
        [key: string]: unknown;
    };
    tool_output: string;
    tool_runtime?: number;
    content: string;
    created_at: string;
};

export type MessageAttachmentPublic = {
    id: string;
    filename: string;
    original_filename: string;
    file_type: string;
    file_size: number;
    storage_key: string;
    thumbnail_key?: (string | null);
    created_at: string;
};

/**
 * Public schema for message display components
 */
export type MessageDisplayComponentPublic = {
    id: string;
    type: MessageDisplayComponentType;
    chart_type: (ChartType | null);
    title: (string | null);
    description: (string | null);
    data: {
        [key: string]: unknown;
    };
    config: {
        [key: string]: unknown;
    };
    position: number;
    created_at: string;
};

/**
 * Enum for display component types (currently supporting only tables and charts)
 */
export type MessageDisplayComponentType = 'table' | 'chart';

/**
 * Schema for creating message feedback
 */
export type MessageFeedbackCreate = {
    /**
     * Type of feedback (good/bad)
     */
    feedback_type: FeedbackType;
    /**
     * Optional reason for the feedback, required when feedback_type is BAD
     */
    reason?: (string | null);
    /**
     * Additional optional comments from the user
     */
    additional_comments?: (string | null);
    message_id: string;
};

/**
 * Public schema for message feedback responses
 */
export type MessageFeedbackPublic = {
    /**
     * Type of feedback (good/bad)
     */
    feedback_type: FeedbackType;
    /**
     * Optional reason for the feedback, required when feedback_type is BAD
     */
    reason?: (string | null);
    /**
     * Additional optional comments from the user
     */
    additional_comments?: (string | null);
    id: string;
    message_id: string;
    user_id: string;
    created_at: string;
    updated_at: string;
};

/**
 * Schema for updating message feedback
 */
export type MessageFeedbackUpdate = {
    feedback_type?: (FeedbackType | null);
    reason?: (string | null);
    additional_comments?: (string | null);
};

export type MessagePublic = {
    id: string;
    content: string;
    role: string;
    is_interrupt: boolean;
    interrupt_message?: (string | null);
    thoughts: Array<MessageAgentThoughtPublic>;
    display_components?: (Array<MessageDisplayComponentPublic> | null);
    attachments?: (Array<MessageAttachmentPublic> | null);
    created_at: string;
};

export type MessagePublicList = {
    messages: Array<MessagePublic>;
    resource_id?: (string | null);
    has_report?: boolean;
    has_dashboard?: boolean;
    total: number;
};

export type MessageResponse = {
    message: string;
};

export type MessageStatistics = {
    total_messages: number;
    average_response_time: number;
    average_input_tokens_per_message: number;
    average_output_tokens_per_message: number;
    daily_message_volume: Array<DailyMessageVolume>;
    token_distribution_by_message_length: Array<TokenDistributionCategory>;
};

export type MessageStreamInput = {
    content: string;
    resume: boolean;
    approve: boolean;
    display_components?: (Array<MessageDisplayComponentPublic> | null);
    attachment_ids?: (Array<(string)> | null);
    resource_id?: (string | null);
};

export type ModuleSetting = {
    key: string;
    value?: {
        [key: string]: unknown;
    };
    created_at?: string;
    updated_at?: (string | null);
};

export type NewPassword = {
    token: string;
    new_password: string;
};

/**
 * Response model for paginated notifications list.
 *
 * Attributes:
 * data: List of notification items
 * count: Total number of items available (before pagination)
 */
export type NotificationList = {
    data: Array<NotificationResponse>;
    count: number;
};

export type NotificationResponse = {
    title: string;
    message: string;
    type?: NotificationType;
    status?: NotificationStatus;
    /**
     * Metadata for the notification
     */
    notification_metadata?: {
        [key: string]: unknown;
    };
    requires_action?: boolean;
    /**
     * URL for direct action
     */
    action_url?: (string | null);
    id: string;
    user_id: string;
    created_at: string;
    updated_at: string;
    read_at?: (string | null);
    expires_at?: (string | null);
};

export type NotificationStatus = 'unread' | 'read' | 'archived';

export type NotificationType = 'info' | 'warning' | 'error' | 'interrupt';

export type OnboardingStatus = {
    is_completed: boolean;
    current_step: number;
};

export type PaginationMeta = {
    /**
     * Current page number (1-based)
     */
    page: number;
    /**
     * Number of items per page
     */
    take: number;
    /**
     * Total number of items available
     */
    total_items: number;
    /**
     * Total number of pages
     */
    total_pages: number;
    /**
     * Whether there is a previous page
     */
    has_previous: boolean;
    /**
     * Whether there is a next page
     */
    has_next: boolean;
    /**
     * Starting index of current page items
     */
    start_index: number;
    /**
     * Ending index of current page items
     */
    end_index: number;
};

export type PaymentMethodResponse = {
    id: string;
    billing_details: BillingDetails;
    card: CardDetails;
    created: number;
    customer: string;
    livemode: boolean;
    type: string;
};

export type PlanChangeRequestCreate = {
    first_name: string;
    last_name: string;
    work_email: string;
    work_title: string;
    company_name: string;
    reason: string;
    current_product_id: string;
    requested_price_id: string;
};

export type PlanChangeRequestResponse = {
    id: string;
    first_name: string;
    last_name: string;
    work_email: string;
    work_title: string;
    company_name: string;
    reason: string;
    status: string;
    customer_id: string;
    current_product_id: string;
    requested_product_id: string;
    created_at: string;
    updated_at: string;
};

/**
 * Request to generate presigned URLs for file uploads
 */
export type PresignedUrlRequest = {
    /**
     * ID of the knowledge base to upload files to
     */
    kb_id: string;
    /**
     * Information about files to generate presigned URLs for
     */
    files: Array<FileInfo>;
};

/**
 * Response with presigned URLs for file uploads
 */
export type PresignedUrlResponse = {
    /**
     * Knowledge base ID
     */
    kb_id: string;
    /**
     * Generated presigned URLs
     */
    presigned_urls: Array<app__schemas__kb__PresignedUrlInfo>;
};

export type PriceResponse = {
    id: string;
    stripe_price_id: string;
    product_id: string;
    active: boolean;
    amount: number;
    currency: string;
    interval: string;
};

export type ProductResponse = {
    id: string;
    name: string;
    description: string;
    stripe_product_id: string;
    active: boolean;
    prices?: (Array<PriceResponse> | null);
    quota_definition?: (QuotaDefinitionResponse | null);
    is_custom: boolean;
};

export type QuotaDefinitionResponse = {
    max_workspaces: number;
    max_members_per_workspace: number;
    max_fast_requests_per_month: (number | null);
};

export type QuotaInfo = {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
};

export type RecommendationCreate = {
    type: string;
    title: string;
    description: string;
    potential_savings: number;
    effort: string;
    risk: string;
    status?: RecommendationStatus;
    resource_id: string;
};

export type RecommendationOveralPublic = {
    total_resource_scanned: number;
    total_well_optimized: number;
    total_optimization_opportunities: number;
    total_estimated_saving_amount: number;
};

export type RecommendationPublic = {
    type: string;
    title: string;
    description: string;
    potential_savings: number;
    effort: string;
    risk: string;
    status?: RecommendationStatus;
    id: string;
    resource_id: string;
    resource_name: string;
    resource_type: string;
    resource_provider: CloudProvider;
    resource_category: ResourceCategory;
};

export type RecommendationsPublic = {
    data: Array<RecommendationPublic>;
    meta: PaginationMeta;
};

export type RecommendationStatus = 'pending' | 'implemented' | 'ignored' | 'in_progress';

export type RecommendationUpdate = {
    type?: (string | null);
    title?: (string | null);
    description?: (string | null);
    potential_savings?: (number | null);
    effort?: (string | null);
    risk?: (string | null);
    status?: (RecommendationStatus | null);
};

export type Report = {
    id?: string;
    conversation_id: string;
    workspace_id: string;
    title: string;
    description?: (string | null);
    sections?: {
        [key: string]: unknown;
    };
    executive_summary?: {
        [key: string]: unknown;
    };
    created_at?: string;
    updated_at?: string;
};

export type ResendActivationRequest = {
    email: string;
    captcha_token: string;
};

export type ResourceCategory = 'COMPUTE' | 'DATABASE' | 'STORAGE' | 'NETWORKING' | 'SERVERLESS' | 'CONTAINER' | 'MESSAGING' | 'MONITORING' | 'SECURITY' | 'MANAGEMENT' | 'ANALYTICS' | 'AI_ML' | 'OTHER';

export type ResourcePublic = {
    id: string;
    resource_id: string;
    name: string;
    region: string;
    provider: CloudProvider;
    category: ResourceCategory;
    type: string;
    status: ResourceStatus;
    total_recommendation: number;
    total_potential_saving: number;
    updated_at: string;
};

export type ResourcePublicSimple = {
    id: string;
    name: string;
};

export type ResourcesPublic = {
    data: Array<ResourcePublic>;
    meta: PaginationMeta;
};

export type ResourceStatus = 'stopped' | 'starting' | 'running' | 'found' | 'deleted';

export type ResourceTypeInfo = {
    resource_type: string;
    category: ResourceCategory;
    display_name: string;
    description: string;
};

export type ResourceTypesResponse = {
    cloud_provider: CloudProvider;
    resource_types: Array<ResourceTypeInfo>;
};

export type RunModeEnum = 'autonomous' | 'agent';

export type ShareResponse = {
    share_id: string;
    is_shared: boolean;
    shared_at: string;
    shared_by: string;
};

export type SimpleMessage = {
    message: string;
};

export type StreamResponse = {
    type: string;
    content?: (string | null);
    message_id?: (string | null);
};

export type SubscriptionStatus = {
    id: string;
    customer_id: string;
    status: string;
    current_period_end: string;
    cancel_at?: (string | null);
    product_name: string;
    product_id?: (string | null);
    price_amount: number;
    price_currency: string;
    price_interval: string;
};

/**
 * Enumeration of possible task categories.
 */
export type TaskCategoryEnum = 'COST_OPTIMIZE' | 'OPERATIONAL' | 'SCALABILITY' | 'SECURITY' | 'OPERATIONAL_EFFICIENCY' | 'OTHER';

export type TaskComplexity = 'Easy' | 'Medium';

export type TaskCouldEnum = 'AWS' | 'AZURE' | 'GCP' | 'ALL';

/**
 * Schema for creating a new task.
 */
export type TaskCreate = {
    title: string;
    description?: (string | null);
    priority?: TaskPriority;
    tags?: Array<(string)>;
    schedule: string;
    agent_config: AgentExecutionConfig;
};

/**
 * Schema for task delete response.
 */
export type TaskDeleteResponse = {
    status?: string;
};

/**
 * Schema for task enable request.
 */
export type TaskEnableRequest = {
    /**
     * Whether to enable or disable the task
     */
    enable: boolean;
};

/**
 * Enumeration of execution statuses for task.
 *
 * Attributes:
 * RUNNING: Currently executing
 * SUCCEEDED: Successfully completed
 * FAILED: Execution failed
 * CANCELLED: Execution cancelled
 * REQUIRED_APPROVAL: Execution requires approval
 */
export type TaskExecutionStatus = 'running' | 'succeeded' | 'failed' | 'cancelled' | 'required_approval';

/**
 * Schema for task histories response.
 */
export type TaskHistoriesResponse = {
    data: Array<TaskHistoryResponse>;
    total: number;
};

/**
 * Schema for task history.
 */
export type TaskHistoryResponse = {
    id: string;
    task_id: string;
    conversation_id: string;
    status: TaskExecutionStatus;
    message?: (string | null);
    celery_task_id?: (string | null);
    start_time: string;
    end_time?: (string | null);
    run_time?: (number | null);
};

/**
 * Schema for paginated task list.
 */
export type TaskList = {
    data?: Array<TaskResponse>;
    total?: number;
};

/**
 * Enumeration of task priority levels.
 *
 * Attributes:
 * LOW (0): Regular priority, no urgency
 * MEDIUM (1): Moderate priority, should be done soon
 * HIGH (2): High priority, urgent attention needed
 * CRITICAL (3): Critical priority, requires immediate attention
 */
export type TaskPriority = 0 | 1 | 2 | 3;

/**
 * Schema for task response.
 */
export type TaskResponse = {
    title: string;
    description?: (string | null);
    priority?: TaskPriority;
    tags?: Array<(string)>;
    id: string;
    workspace_id: string;
    owner_id: string;
    scheduled_status?: (TaskScheduledStatus | null);
    execution_status?: (TaskExecutionStatus | null);
    error?: (string | null);
    last_run?: (string | null);
    next_run?: (string | null);
    schedule: string;
    agent_config: AgentExecutionConfig;
    enable?: boolean;
    created_at: string;
    updated_at: string;
    created_by: string;
    updated_by: string;
};

/**
 * Enumeration of scheduled statuses for task.
 */
export type TaskScheduledStatus = 'pending' | 'scheduled';

/**
 * Enumeration of possible task services.
 */
export type TaskServiceEnum = 'ALL' | 'OTHER' | 'COMPUTE' | 'STORAGE' | 'SERVERLESS' | 'DATABASE' | 'NETWORK' | 'MESSAGING' | 'MANAGEMENT' | 'BILLING' | 'CROSS_SERVICE' | 'MONITORING' | 'STREAMING' | 'SECURITY';

/**
 * Schema for task stop request.
 */
export type TaskStopRequest = {
    /**
     * Conversation ID to stop
     */
    conversation_id: string;
};

/**
 * Schema for task stop response.
 */
export type TaskStopResponse = {
    task_id: string;
    conversation_id: string;
    status: string;
};

export type TaskTemplate = {
    title: string;
    description: string;
    icon: string;
    impact: string;
    complexity: TaskComplexity;
    duration: string;
};

export type TaskTemplateCreate = {
    task: string;
    category?: (TaskCategoryEnum | null);
    service?: (TaskServiceEnum | null);
    service_name?: (string | null);
    cloud: TaskCouldEnum;
    run_mode: RunModeEnum;
    schedule?: (string | null);
    context: string;
};

export type TaskTemplateList = {
    data: Array<app__schemas__task_template__TaskTemplateResponse>;
    total: number;
};

export type TaskTemplateSelection = {
    selected_template_title?: (string | null);
    action: string;
};

export type TaskTemplateUpdate = {
    task?: (string | null);
    category?: (TaskCategoryEnum | null);
    service?: (TaskServiceEnum | null);
    run_mode?: (RunModeEnum | null);
    schedule?: (string | null);
    context?: (string | null);
};

/**
 * Schema for updating an existing task.
 */
export type TaskUpdate = {
    title?: (string | null);
    description?: (string | null);
    schedule?: (string | null);
};

export type Token = {
    access_token: string;
    token_type?: string;
    workspace_id?: (string | null);
    is_first_login?: boolean;
    slack_oauth?: boolean;
    app_id?: (string | null);
    team_id?: (string | null);
};

export type TokenDistributionCategory = {
    category: string;
    percentage: number;
};

export type TokenUsageCreate = {
    /**
     * Unique identifier of the associated message
     */
    message_id: string;
    /**
     * Number of tokens in the input text
     */
    input_tokens: number;
    /**
     * Number of tokens in the output text
     */
    output_tokens: number;
    /**
     * Identifier of the AI model used
     */
    model_id: string;
};

/**
 * Schema for token usage response.
 *
 * Attributes:
 * id: Unique identifier for the usage record
 * message_id: ID of the associated message
 * input_tokens: Number of tokens in input text
 * output_tokens: Number of tokens in output text
 * model_id: ID of the AI model used
 * total_tokens: Total number of tokens used
 * created_at: Timestamp of record creation
 */
export type TokenUsageResponse = {
    /**
     * Unique identifier for the usage record
     */
    id: string;
    /**
     * ID of the associated message
     */
    message_id: string;
    /**
     * Number of tokens in the input text
     */
    input_tokens: number;
    /**
     * Number of tokens in the output text
     */
    output_tokens: number;
    /**
     * Identifier of the AI model used
     */
    model_id: string;
    /**
     * ID of the workspace
     */
    workspace_id: string;
    /**
     * Timestamp of record creation
     */
    created_at: string;
    /**
     * Timestamp of last update
     */
    updated_at?: (string | null);
    /**
     * Calculate total tokens from input and output tokens.
     */
    readonly total_tokens: number;
};

export type UpdatePassword = {
    current_password: string;
    new_password: string;
};

export type UploadedAttachmentInfo = {
    /**
     * Client-side unique ID for the file.
     */
    file_id: string;
    /**
     * Sanitized name of the file.
     */
    filename: string;
    /**
     * The key for the object in storage.
     */
    storage_key: string;
    /**
     * MIME type of the file.
     */
    content_type: string;
    /**
     * Size of the file in bytes.
     */
    file_size: number;
};

/**
 * Information about a successfully uploaded file
 */
export type UploadedFileInfo = {
    /**
     * Client-side ID for tracking this file
     */
    file_id: string;
    /**
     * Original filename
     */
    filename: string;
    /**
     * Storage key for the uploaded file
     */
    storage_key: string;
    /**
     * File MIME type
     */
    content_type?: (string | null);
    /**
     * File size in bytes
     */
    file_size?: (number | null);
};

export type URLsUploadRequest = {
    /**
     * URLs to crawl (required if source_type is website)
     */
    urls?: (Array<(string)> | null);
    /**
     * Whether to enable deep crawling for each URL
     */
    deep_crawls?: (Array<(boolean)> | null);
};

/**
 * Response schema for usage quota information.
 */
export type UsageQuotaResponse = {
    id: string;
    user_id: string;
    quota_used_messages: number;
    quota_used_tokens: number;
    reset_at: string;
    created_at: string;
    updated_at?: (string | null);
};

/**
 * Response schema for usage statistics.
 */
export type UsageStatistics = {
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
    quota_limit: number;
    quota_used: number;
    quota_remaining: number;
    usage_percentage: number;
    daily_token_usage: Array<DailyTokenUsage>;
    agent_type_stats: Array<AgentTypeUsage>;
};

export type UserCreate = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    password: string;
};

export type UserDetail = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    id: string;
    workspaces?: (Array<Workspace> | null);
    own_workspaces?: (Array<Workspace> | null);
};

export type UserPublic = {
    email: string;
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    id: string;
};

export type UserRegister = {
    email: string;
    password: string;
    full_name?: (string | null);
};

export type UsersPublic = {
    data: Array<UserPublic>;
    count: number;
};

export type UserUpdate = {
    email?: (string | null);
    is_active?: boolean;
    is_email_verified?: boolean;
    is_superuser?: boolean;
    last_login_time?: (string | null);
    full_name?: (string | null);
    avatar_url?: (string | null);
    password?: (string | null);
};

export type UserUpdateMe = {
    full_name?: (string | null);
    email?: (string | null);
    avatar_url?: (string | null);
};

export type ValidationError = {
    loc: Array<(string | number)>;
    msg: string;
    type: string;
};

export type Workspace = {
    name: string;
    description?: (string | null);
    organization_name?: (string | null);
    provider?: (CloudProvider | null);
    id?: string;
    owner_id: string;
    created_at?: string;
    updated_at?: string;
    is_default?: boolean;
    is_deleted?: boolean;
};

export type WorkspaceBuiltInToolResponse = {
    id: string;
    required_permission: boolean;
    builtin_tool: BuiltInToolPublic;
};

export type WorkspaceCreate = {
    name: string;
    description?: (string | null);
    organization_name?: (string | null);
    provider?: (CloudProvider | null);
};

export type WorkspaceDetail = {
    name: string;
    description?: (string | null);
    organization_name?: (string | null);
    provider?: (CloudProvider | null);
    id: string;
    is_default: boolean;
    is_deleted?: boolean;
    created_at?: string;
    updated_at?: string;
    aws_account?: (AWSAccountDetail | null);
    gcp_account?: (GCPAccountDetail | null);
    settings: (WorkspaceSetting | null);
};

export type WorkspacePublic = {
    name: string;
    description?: (string | null);
    organization_name?: (string | null);
    provider?: (CloudProvider | null);
    id: string;
    is_default: boolean;
    is_deleted?: boolean;
    created_at?: string;
    updated_at?: string;
};

/**
 * Settings for a workspace
 */
export type WorkspaceSetting = {
    id?: string;
    workspace_id: string;
    provider?: CloudProvider;
    regions?: Array<(string)>;
    types?: Array<(string)>;
    cron_pattern: string;
};

export type WorkspacesPublic = {
    data: Array<WorkspacePublic>;
    count: number;
};

export type WorkspaceUpdate = {
    name: string;
    description?: (string | null);
    organization_name?: (string | null);
    provider?: (CloudProvider | null);
};

export type AgentsReadAgentsResponse = (AgentsPublic);

export type AgentsUpdateAgentInstructionsData = {
    agentId: string;
    requestBody: AgentInstructionsUpdate;
};

export type AgentsUpdateAgentInstructionsResponse = (AgentPublic);

export type AgentsUpdateAgentStatusData = {
    agentId: string;
    requestBody: AgentStatusUpdate;
};

export type AgentsUpdateAgentStatusResponse = (AgentPublic);

export type AgentsBuiltinToolsGetWorkspaceAgentBuiltinToolsResponse = (AgentsBuiltInToolsResponse);

export type AgentsBuiltinToolsBulkUpdateAgentBuiltinToolsData = {
    agentId: string;
    requestBody: AgentBuiltInToolsUpdateRequest;
};

export type AgentsBuiltinToolsBulkUpdateAgentBuiltinToolsResponse = (AgentBuiltInToolsBulkUpdateResponse);

export type AgentsConnectionsGetWorkspaceAgentConnectionsResponse = (AgentsConnectionsResponse);

export type AgentsConnectionsCreateAgentConnectionData = {
    agentId: string;
    requestBody: AgentConnectionCreateRequest;
};

export type AgentsConnectionsCreateAgentConnectionResponse = (AgentConnectionResponse);

export type AgentsConnectionsDeleteAgentConnectionData = {
    agentId: string;
    connectionId: string;
};

export type AgentsConnectionsDeleteAgentConnectionResponse = (AgentConnectionResponse);

export type AlertsGetAlertStatusSummaryResponse = (AlertStatusSummary);

export type AlertsCreateAlertData = {
    requestBody: AlertCreate;
};

export type AlertsCreateAlertResponse = (AlertResponse);

export type AlertsListAlertsData = {
    limit?: number;
    severity?: (AlertSeverity | null);
    skip?: number;
    /**
     * Field to sort by
     */
    sortBy?: string;
    /**
     * Sort in descending order
     */
    sortDesc?: boolean;
    status?: (AlertStatus | null);
};

export type AlertsListAlertsResponse = (AlertList);

export type AlertsGetAlertData = {
    alertId: string;
};

export type AlertsGetAlertResponse = (AlertResponse);

export type AlertsUpdateAlertData = {
    alertId: string;
    requestBody: AlertUpdate;
};

export type AlertsUpdateAlertResponse = (AlertResponse);

export type AlertsDeleteAlertData = {
    alertId: string;
};

export type AlertsDeleteAlertResponse = (unknown);

export type AlertsUpdateAlertStatusData = {
    alertId: string;
    status: AlertStatus;
};

export type AlertsUpdateAlertStatusResponse = (AlertResponse);

export type AlertsMarkAllAlertsAcknowledgedResponse = (unknown);

export type AttachmentsGenerateAttachmentPresignedUrlsData = {
    requestBody: AttachmentPresignedUrlRequest;
};

export type AttachmentsGenerateAttachmentPresignedUrlsResponse = (AttachmentPresignedUrlResponse);

export type AttachmentsConfirmAttachmentUploadsData = {
    requestBody: AttachmentConfirmRequest;
};

export type AttachmentsConfirmAttachmentUploadsResponse = (app__schemas__message_attachment__TaskStatusResponse);

export type AttachmentsGetAttachmentTaskStatusData = {
    taskId: string;
};

export type AttachmentsGetAttachmentTaskStatusResponse = (app__schemas__message_attachment__TaskStatusResponse);

export type AttachmentsGetAttachmentDownloadUrlData = {
    attachmentId: string;
};

export type AttachmentsGetAttachmentDownloadUrlResponse = (AttachmentDownloadResponse);

export type AttachmentsGetAttachmentMetadataData = {
    attachmentId: string;
};

export type AttachmentsGetAttachmentMetadataResponse = (AttachmentMetadataResponse);

export type AuthRegisterData = {
    requestBody: UserRegister;
};

export type AuthRegisterResponse = (ActivationResponse);

export type AuthActivateAccountData = {
    token: string;
};

export type AuthActivateAccountResponse = (ActivationResult);

export type AuthResendActivationData = {
    requestBody: ResendActivationRequest;
};

export type AuthResendActivationResponse = (ActivationResponse);

export type AutonomousAgentsCreateConversationData = {
    requestBody: ConversationCreateRequest;
};

export type AutonomousAgentsCreateConversationResponse = (ConversationPublic);

export type AutonomousAgentsGetConversationsData = {
    agentId?: (string | null);
    /**
     * Maximum number of records to return
     */
    limit?: number;
    resourceId?: (string | null);
    search?: (string | null);
    /**
     * Number of records to skip for pagination
     */
    skip?: number;
};

export type AutonomousAgentsGetConversationsResponse = (ConversationsPublic);

export type AutonomousAgentsRenameConversationData = {
    conversationId: string;
    requestBody: ConversationRenameRequest;
};

export type AutonomousAgentsRenameConversationResponse = (ConversationPublic);

export type AutonomousAgentsDeleteConversationData = {
    conversationId: string;
};

export type AutonomousAgentsDeleteConversationResponse = ({
    [key: string]: (string);
});

export type AutonomousAgentsGetMessagesHistoryData = {
    conversationId: string;
    /**
     * Maximum number of records to return
     */
    limit?: number;
    /**
     * Number of records to skip for pagination
     */
    skip?: number;
};

export type AutonomousAgentsGetMessagesHistoryResponse = (MessagePublicList);

export type AutonomousAgentsChatStreamData = {
    conversationId: string;
    requestBody: MessageStreamInput;
};

export type AutonomousAgentsChatStreamResponse = (StreamResponse);

export type AwsAccountsUpdateAwsAccountData = {
    requestBody: AWSAccountUpdate;
};

export type AwsAccountsUpdateAwsAccountResponse = (AWSAccountPublic);

export type AwsAccountsCreateAwsAccountData = {
    requestBody: AWSAccountCreate;
};

export type AwsAccountsCreateAwsAccountResponse = (AWSAccountPublic);

export type BuiltinToolsGetWorkspaceBuiltinToolsResponse = (Array<WorkspaceBuiltInToolResponse>);

export type BuiltinToolsUpdateWorkspaceBuiltinToolData = {
    requestBody: BuiltInToolUpdateRequest;
    workspaceBuiltinToolId: string;
};

export type BuiltinToolsUpdateWorkspaceBuiltinToolResponse = (boolean);

export type CloudSyncConfigGetResourceTypesData = {
    /**
     * Cloud provider (AWS, GCP, AZURE)
     */
    cloudProvider: string;
};

export type CloudSyncConfigGetResourceTypesResponse = (ResourceTypesResponse);

export type CloudSyncConfigGetWorkspaceConfigsResponse = (Array<CloudSyncConfigPublic>);

export type CloudSyncConfigCreateOrUpdateConfigData = {
    requestBody: CloudSyncConfigCreate;
};

export type CloudSyncConfigCreateOrUpdateConfigResponse = (CloudSyncConfigPublic);

export type CloudSyncConfigUpdateConfigData = {
    configId: string;
    requestBody: CloudSyncConfigUpdate;
};

export type CloudSyncConfigUpdateConfigResponse = (CloudSyncConfigPublic);

export type CloudSyncConfigDeleteConfigData = {
    configId: string;
};

export type CloudSyncConfigDeleteConfigResponse = (Message);

export type CloudSyncConfigTriggerManualSyncData = {
    configId: string;
};

export type CloudSyncConfigTriggerManualSyncResponse = (Message);

export type ConnectionGetConnectionsResponse = (ConnectionsPublic);

export type ConnectionCreateConnectionData = {
    requestBody: ConnectionCreate;
};

export type ConnectionCreateConnectionResponse = (ConnectionPublic);

export type ConnectionGetBuiltinConnectionsData = {
    connectionType?: Array<(string)>;
};

export type ConnectionGetBuiltinConnectionsResponse = (ConnectionsPublic);

export type ConnectionInstallBuiltinConnectionData = {
    builtinId: string;
    requestBody?: (BuiltinInstallRequest | null);
};

export type ConnectionInstallBuiltinConnectionResponse = (ConnectionPublic);

export type ConnectionGetConnectionData = {
    connId: string;
};

export type ConnectionGetConnectionResponse = (ConnectionPublic);

export type ConnectionUpdateConnectionData = {
    connId: string;
    requestBody: ConnectionUpdate;
};

export type ConnectionUpdateConnectionResponse = (ConnectionPublic);

export type ConnectionDeleteConnectionData = {
    connId: string;
};

export type ConnectionDeleteConnectionResponse = (Message);

export type ConsoleProxyGetFilesResponse = (FileListResponse);

export type ConsoleProxyGetFileContentData = {
    /**
     * File path
     */
    path: string;
};

export type ConsoleProxyGetFileContentResponse = (FileContentResponse);

export type DashboardsGetDashboardByConversationData = {
    conversationId: string;
};

export type DashboardsGetDashboardByConversationResponse = (Dashboard);

export type GcpAccountsUpdateGcpAccountData = {
    requestBody: GCPAccountUpdate;
};

export type GcpAccountsUpdateGcpAccountResponse = (GCPAccountPublic);

export type GcpAccountsCreateGcpAccountData = {
    requestBody: GCPAccountCreate;
};

export type GcpAccountsCreateGcpAccountResponse = (GCPAccountPublic);

export type GoogleGoogleLoginResponse = (unknown);

export type GoogleGoogleCallbackResponse = (Token);

export type KnowledgeBaseCreateKbData = {
    requestBody: KBCreate;
};

export type KnowledgeBaseCreateKbResponse = (KBRead);

export type KnowledgeBaseGetKbsData = {
    accessLevel?: (string | null);
    limit?: number;
    search?: (string | null);
    skip?: number;
    usageMode?: (string | null);
};

export type KnowledgeBaseGetKbsResponse = (KBsRead);

export type KnowledgeBaseGetAvailableUsersResponse = (AvailableUsersCurrentWorkspace);

export type KnowledgeBaseGetPointUsageResponse = ({
    [key: string]: unknown;
});

export type KnowledgeBaseGetKbByIdData = {
    kbId: string;
};

export type KnowledgeBaseGetKbByIdResponse = (KBRead);

export type KnowledgeBaseUpdateKbData = {
    kbId: string;
    requestBody: KBUpdate;
};

export type KnowledgeBaseUpdateKbResponse = (KBRead);

export type KnowledgeBaseDeleteKbData = {
    kbId: string;
};

export type KnowledgeBaseDeleteKbResponse = ({
    [key: string]: unknown;
});

export type KnowledgeBaseGeneratePresignedUrlsData = {
    kbId: string;
    requestBody: PresignedUrlRequest;
};

export type KnowledgeBaseGeneratePresignedUrlsResponse = (PresignedUrlResponse);

export type KnowledgeBaseConfirmFileUploadsData = {
    kbId: string;
    requestBody: ConfirmUploadsRequest;
};

export type KnowledgeBaseConfirmFileUploadsResponse = (app__schemas__kb__TaskStatusResponse);

export type KnowledgeBaseUploadUrlsData = {
    kbId: string;
    requestBody: URLsUploadRequest;
};

export type KnowledgeBaseUploadUrlsResponse = (app__schemas__kb__TaskStatusResponse);

export type KnowledgeBaseListDocumentsData = {
    kbId: string;
    limit?: number;
    search?: (string | null);
    skip?: number;
};

export type KnowledgeBaseListDocumentsResponse = (DocumentsKBRead);

export type KnowledgeBaseGetDocumentContentData = {
    kbId: string;
    objectName: string;
};

export type KnowledgeBaseGetDocumentContentResponse = (string);

export type KnowledgeBaseDeleteDocumentData = {
    documentId: string;
    kbId: string;
    objectName: string;
};

export type KnowledgeBaseDeleteDocumentResponse = ({
    [key: string]: unknown;
});

export type KnowledgeBaseGetTaskStatusData = {
    taskId: string;
};

export type KnowledgeBaseGetTaskStatusResponse = (app__schemas__kb__TaskStatusResponse);

export type LoginLoginAccessTokenData = {
    formData: Body_login_login_access_token;
};

export type LoginLoginAccessTokenResponse = (Token);

export type LoginTestTokenResponse = (UserPublic);

export type LoginRecoverPasswordData = {
    email: string;
};

export type LoginRecoverPasswordResponse = (Message);

export type LoginResetPasswordData = {
    requestBody: NewPassword;
};

export type LoginResetPasswordResponse = (Message);

export type LoginRecoverPasswordHtmlContentData = {
    email: string;
};

export type LoginRecoverPasswordHtmlContentResponse = (string);

export type MessageFeedbackGetMessageFeedbackData = {
    messageId: string;
};

export type MessageFeedbackGetMessageFeedbackResponse = ((MessageFeedbackPublic | null));

export type MessageFeedbackUpdateMessageFeedbackData = {
    messageId: string;
    requestBody: MessageFeedbackUpdate;
};

export type MessageFeedbackUpdateMessageFeedbackResponse = (MessageFeedbackPublic);

export type MessageFeedbackDeleteMessageFeedbackData = {
    messageId: string;
};

export type MessageFeedbackDeleteMessageFeedbackResponse = (unknown);

export type MessageFeedbackCreateMessageFeedbackData = {
    requestBody: MessageFeedbackCreate;
};

export type MessageFeedbackCreateMessageFeedbackResponse = (MessageFeedbackPublic);

export type ModuleSettingGetModuleSettingsResponse = (Array<ModuleSetting>);

export type NotificationsListNotificationsData = {
    limit?: number;
    requestBody?: Body_notifications_list_notifications;
    requiresAction?: (boolean | null);
    skip?: number;
    timeframe?: (string | null);
};

export type NotificationsListNotificationsResponse = (NotificationList);

export type NotificationsMarkNotificationReadData = {
    notificationId: string;
};

export type NotificationsMarkNotificationReadResponse = (NotificationResponse);

export type NotificationsMarkAllNotificationsReadData = {
    requestBody?: (Array<NotificationType> | null);
};

export type NotificationsMarkAllNotificationsReadResponse = (unknown);

export type OnboardingGetOnboardingStatusResponse = (OnboardingStatus);

export type OnboardingCreateWorkspaceData = {
    requestBody: WorkspaceCreate;
};

export type OnboardingCreateWorkspaceResponse = (WorkspacePublic);

export type OnboardingConnectAwsData = {
    requestBody: AWSOnboardingCreate;
};

export type OnboardingConnectAwsResponse = (unknown);

export type OnboardingConnectGcpData = {
    requestBody: GCPOnboardingCreate;
};

export type OnboardingConnectGcpResponse = (unknown);

export type OnboardingConnectAzureData = {
    requestBody: AzureOnboardingCreate;
};

export type OnboardingConnectAzureResponse = (unknown);

export type OnboardingGetTaskTemplateResponse = (app__schemas__onboarding__TaskTemplateResponse);

export type OnboardingCompleteTaskTemplateData = {
    requestBody: TaskTemplateSelection;
};

export type OnboardingCompleteTaskTemplateResponse = (unknown);

export type QuotasCreateUsageData = {
    requestBody: TokenUsageCreate;
};

export type QuotasCreateUsageResponse = (TokenUsageResponse);

export type QuotasGetMessagesStatisticsData = {
    endDate?: (string | null);
    startDate?: (string | null);
};

export type QuotasGetMessagesStatisticsResponse = (MessageStatistics);

export type QuotasCreateUsageQuotaData = {
    userId: string;
};

export type QuotasCreateUsageQuotaResponse = (UsageQuotaResponse);

export type QuotasGetUsageQuotaData = {
    userId: string;
};

export type QuotasGetUsageQuotaResponse = (UsageQuotaResponse);

export type QuotasResetUserQuotaData = {
    userId: string;
};

export type QuotasResetUserQuotaResponse = (UsageQuotaResponse);

export type QuotasGetUsageStatisticsData = {
    endDate?: (string | null);
    startDate?: (string | null);
    userId: string;
};

export type QuotasGetUsageStatisticsResponse = (UsageStatistics);

export type QuotasGetQuotaInfoData = {
    userId: string;
};

export type QuotasGetQuotaInfoResponse = (QuotaInfo);

export type RecommendationsGetRecomendationOveralResponse = (RecommendationOveralPublic);

export type RecommendationsReadRecommendationsData = {
    endDate?: (string | null);
    limit?: number;
    orderBy?: (string | null);
    orderDirection?: string;
    resourceId?: Array<(string)>;
    resourceType?: Array<(string)>;
    search?: (string | null);
    skip?: number;
    startDate?: (string | null);
    status?: Array<(string)>;
};

export type RecommendationsReadRecommendationsResponse = (RecommendationsPublic);

export type RecommendationsCreateRecommendationData = {
    requestBody: RecommendationCreate;
};

export type RecommendationsCreateRecommendationResponse = (RecommendationPublic);

export type RecommendationsReadRecommendationData = {
    id: string;
};

export type RecommendationsReadRecommendationResponse = (RecommendationPublic);

export type RecommendationsUpdateRecommendationData = {
    id: string;
    requestBody: RecommendationUpdate;
};

export type RecommendationsUpdateRecommendationResponse = (RecommendationPublic);

export type RecommendationsDeleteRecommendationData = {
    id: string;
};

export type RecommendationsDeleteRecommendationResponse = (SimpleMessage);

export type RecommendationsUpdateRecommendationStatusData = {
    id: string;
    status: RecommendationStatus;
};

export type RecommendationsUpdateRecommendationStatusResponse = (RecommendationPublic);

export type ReportsGetReportByConversationData = {
    conversationId: string;
};

export type ReportsGetReportByConversationResponse = (Report);

export type ResourcesReadResourcesData = {
    limit?: number;
    name?: (string | null);
    region?: Array<(string)>;
    resourceType?: Array<(string)>;
    skip?: number;
    status?: Array<(string)>;
};

export type ResourcesReadResourcesResponse = (ResourcesPublic);

export type SampleDataCreateSampleResourcesData = {
    /**
     * Number of sample resources to create
     */
    count?: number;
};

export type SampleDataCreateSampleResourcesResponse = (unknown);

export type SampleDataClearSampleResourcesData = {
    /**
     * Confirm deletion of all resources
     */
    confirm?: boolean;
};

export type SampleDataClearSampleResourcesResponse = (unknown);

export type SampleDataPreviewSampleResourcesData = {
    /**
     * Number of sample resources to preview
     */
    count?: number;
};

export type SampleDataPreviewSampleResourcesResponse = (unknown);

export type ShareChatCreateShareLinkData = {
    conversationId: string;
};

export type ShareChatCreateShareLinkResponse = (ShareResponse);

export type ShareChatRevokeShareLinkData = {
    conversationId: string;
};

export type ShareChatRevokeShareLinkResponse = (unknown);

export type ShareChatGetShareLinkData = {
    conversationId: string;
};

export type ShareChatGetShareLinkResponse = (ShareResponse);

export type ShareChatGetSharedConversationData = {
    limit?: number;
    shareId: string;
    skip?: number;
};

export type ShareChatGetSharedConversationResponse = (MessagePublicList);

export type SubscriptionsGetAvailablePlansResponse = (Array<ProductResponse>);

export type SubscriptionsGetUserSubscriptionStatusResponse = (Array<SubscriptionStatus>);

export type SubscriptionsGetWorkspaceSubscriptionStatusData = {
    workspaceId: string;
};

export type SubscriptionsGetWorkspaceSubscriptionStatusResponse = (Array<SubscriptionStatus>);

export type SubscriptionsCreateCheckoutSessionData = {
    priceId: string;
};

export type SubscriptionsCreateCheckoutSessionResponse = (CheckoutSessionResponse);

export type SubscriptionsGetUserPaymentMethodsData = {
    paymentType?: string;
};

export type SubscriptionsGetUserPaymentMethodsResponse = (Array<PaymentMethodResponse>);

export type SubscriptionsGetUserInvoicesData = {
    limit?: number;
    status?: (string | null);
};

export type SubscriptionsGetUserInvoicesResponse = (Array<InvoiceResponse>);

export type SubscriptionsSubmitEnterpriseEnquiryData = {
    requestBody: EnterpriseEnquiryRequest;
};

export type SubscriptionsSubmitEnterpriseEnquiryResponse = (EnterpriseEnquiryMessageResponse);

export type SubscriptionsSubmitPlanChangeRequestData = {
    requestBody: PlanChangeRequestCreate;
};

export type SubscriptionsSubmitPlanChangeRequestResponse = (PlanChangeRequestResponse);

export type SubscriptionsWebhookResponse = (unknown);

export type SubscriptionsCancelSubscriptionResponse = ({
    [key: string]: unknown;
});

export type TasksCreateTaskData = {
    requestBody: TaskCreate;
};

export type TasksCreateTaskResponse = (TaskResponse);

export type TasksListTasksData = {
    executionStatus?: (TaskExecutionStatus | null);
    limit?: number;
    search?: (string | null);
    skip?: number;
    timezone?: string;
};

export type TasksListTasksResponse = (TaskList);

export type TasksGetTaskData = {
    taskId: string;
    timezone?: string;
};

export type TasksGetTaskResponse = (TaskResponse);

export type TasksUpdateTaskData = {
    requestBody: TaskUpdate;
    taskId: string;
};

export type TasksUpdateTaskResponse = (TaskResponse);

export type TasksDeleteTaskData = {
    taskId: string;
};

export type TasksDeleteTaskResponse = (TaskDeleteResponse);

export type TasksGetTaskAverageRunTimeData = {
    taskId: string;
};

export type TasksGetTaskAverageRunTimeResponse = (number);

export type TasksGetTaskHistoriesData = {
    limit?: number;
    skip?: number;
    taskId: string;
    timezone?: string;
};

export type TasksGetTaskHistoriesResponse = (TaskHistoriesResponse);

export type TasksUpdateTaskEnableData = {
    requestBody: TaskEnableRequest;
    taskId: string;
};

export type TasksUpdateTaskEnableResponse = (TaskResponse);

export type TasksStopTaskExecutionData = {
    requestBody: TaskStopRequest;
    taskId: string;
};

export type TasksStopTaskExecutionResponse = (TaskStopResponse);

export type TaskTemplatesGenerateData = {
    input: string;
};

export type TaskTemplatesGenerateResponse = (app__schemas__task_template__TaskTemplateResponse);

export type TaskTemplatesCreateTemplateData = {
    isDefault?: boolean;
    requestBody: TaskTemplateCreate;
};

export type TaskTemplatesCreateTemplateResponse = (app__schemas__task_template__TaskTemplateResponse);

export type TaskTemplatesListTemplatesData = {
    category?: (Array<TaskCategoryEnum> | null);
    includeDefaults?: boolean;
    limit?: number;
    searchQuery?: (string | null);
    services?: (Array<TaskServiceEnum> | null);
    skip?: number;
};

export type TaskTemplatesListTemplatesResponse = (TaskTemplateList);

export type TaskTemplatesGetTemplateData = {
    templateId: string;
};

export type TaskTemplatesGetTemplateResponse = (app__schemas__task_template__TaskTemplateResponse);

export type TaskTemplatesUpdateTemplateData = {
    requestBody: TaskTemplateUpdate;
    templateId: string;
};

export type TaskTemplatesUpdateTemplateResponse = (app__schemas__task_template__TaskTemplateResponse);

export type TaskTemplatesDeleteTemplateData = {
    templateId: string;
};

export type TaskTemplatesDeleteTemplateResponse = (unknown);

export type UsersReadUsersData = {
    limit?: number;
    skip?: number;
};

export type UsersReadUsersResponse = (UsersPublic);

export type UsersCreateUserData = {
    requestBody: UserCreate;
};

export type UsersCreateUserResponse = (UserPublic);

export type UsersReadUserMeResponse = (UserDetail);

export type UsersDeleteUserMeResponse = (Message);

export type UsersUpdateUserMeData = {
    requestBody: UserUpdateMe;
};

export type UsersUpdateUserMeResponse = (UserPublic);

export type UsersUpdatePasswordMeData = {
    requestBody: UpdatePassword;
};

export type UsersUpdatePasswordMeResponse = (Message);

export type UsersReadUserByIdData = {
    userId: string;
};

export type UsersReadUserByIdResponse = (UserPublic);

export type UsersUpdateUserData = {
    requestBody: UserUpdate;
    userId: string;
};

export type UsersUpdateUserResponse = (UserPublic);

export type UsersDeleteUserData = {
    userId: string;
};

export type UsersDeleteUserResponse = (Message);

export type UsersSwitchWorkspaceData = {
    workspaceId: string;
};

export type UsersSwitchWorkspaceResponse = (Token);

export type UtilsHealthCheckResponse = (boolean);

export type UtilsGetConstantCategoriesResponse = (ConstantCategoriesResponse);

export type UtilsGetConstantCategoryData = {
    category: string;
};

export type UtilsGetConstantCategoryResponse = (ConstantCategory);

export type UtilsGetAllConstantsResponse = (AllConstantsResponse);

export type WorkspacesGetWorkspacesData = {
    limit?: number;
    skip?: number;
};

export type WorkspacesGetWorkspacesResponse = (WorkspacesPublic);

export type WorkspacesCreateWorkspaceData = {
    requestBody: WorkspaceCreate;
};

export type WorkspacesCreateWorkspaceResponse = (WorkspacePublic);

export type WorkspacesGetWorkspaceDetailsData = {
    workspaceId: string;
};

export type WorkspacesGetWorkspaceDetailsResponse = (WorkspaceDetail);

export type WorkspacesUpdateWorkspaceData = {
    requestBody: WorkspaceUpdate;
    workspaceId: string;
};

export type WorkspacesUpdateWorkspaceResponse = (WorkspacePublic);

export type WorkspacesDeleteWorkspaceData = {
    workspaceId: string;
};

export type WorkspacesDeleteWorkspaceResponse = (MessageResponse);