// This file is auto-generated by @hey-api/openapi-ts

export const AWSAccountCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        account_id: {
            type: 'string',
            maxLength: 12,
            minLength: 12,
            title: 'Account Id'
        },
        access_key_id: {
            type: 'string',
            maxLength: 128,
            title: 'Access Key Id'
        },
        secret_access_key: {
            type: 'string',
            maxLength: 256,
            title: 'Secret Access Key'
        },
        regions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Regions',
            default: []
        },
        types: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Types',
            default: []
        },
        cron_pattern: {
            type: 'string',
            maxLength: 20,
            minLength: 1,
            title: 'Cron Pattern'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'account_id', 'access_key_id', 'secret_access_key', 'cron_pattern'],
    title: 'AWSAccountCreate'
} as const;

export const AWSAccountDetailSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        access_key_id: {
            type: 'string',
            title: 'Access Key Id'
        },
        secret_access_key: {
            type: 'string',
            title: 'Secret Access Key'
        },
        account_id: {
            type: 'string',
            title: 'Account Id'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'id', 'access_key_id', 'secret_access_key', 'account_id'],
    title: 'AWSAccountDetail'
} as const;

export const AWSAccountPublicSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'id'],
    title: 'AWSAccountPublic'
} as const;

export const AWSAccountUpdateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        account_id: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 12,
                    minLength: 12
                },
                {
                    type: 'null'
                }
            ],
            title: 'Account Id'
        },
        access_key_id: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 128
                },
                {
                    type: 'null'
                }
            ],
            title: 'Access Key Id'
        },
        secret_access_key: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 256
                },
                {
                    type: 'null'
                }
            ],
            title: 'Secret Access Key'
        },
        regions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Regions',
            default: []
        },
        types: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Types',
            default: []
        },
        cron_pattern: {
            type: 'string',
            maxLength: 20,
            minLength: 1,
            title: 'Cron Pattern'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'cron_pattern'],
    title: 'AWSAccountUpdate'
} as const;

export const AWSOnboardingCreateSchema = {
    properties: {
        aws_access_key_id: {
            type: 'string',
            title: 'Aws Access Key Id'
        },
        aws_secret_access_key: {
            type: 'string',
            title: 'Aws Secret Access Key'
        },
        aws_default_region: {
            type: 'string',
            title: 'Aws Default Region'
        }
    },
    type: 'object',
    required: ['aws_access_key_id', 'aws_secret_access_key', 'aws_default_region'],
    title: 'AWSOnboardingCreate'
} as const;

export const AccountEnvironementSchema = {
    type: 'string',
    enum: ['production', 'staging', 'development'],
    title: 'AccountEnvironement'
} as const;

export const ActivationResponseSchema = {
    properties: {
        message: {
            type: 'string',
            title: 'Message'
        },
        expires_at: {
            type: 'string',
            format: 'date-time',
            title: 'Expires At'
        }
    },
    type: 'object',
    required: ['message', 'expires_at'],
    title: 'ActivationResponse'
} as const;

export const ActivationResultSchema = {
    properties: {
        success: {
            type: 'boolean',
            title: 'Success'
        },
        message: {
            type: 'string',
            title: 'Message'
        },
        redirect_url: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Redirect Url'
        },
        welcome_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Welcome Message'
        }
    },
    type: 'object',
    required: ['success', 'message'],
    title: 'ActivationResult'
} as const;

export const AddressSchema = {
    properties: {
        city: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'City'
        },
        country: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Country'
        },
        line1: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Line1'
        },
        line2: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Line2'
        },
        postal_code: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Postal Code'
        },
        state: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'State'
        }
    },
    type: 'object',
    title: 'Address'
} as const;

export const AgentBuiltInToolBulkUpdateResultSchema = {
    properties: {
        workspace_builtin_tool_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Builtin Tool Id'
        },
        success: {
            type: 'boolean',
            title: 'Success'
        },
        error_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Error Message'
        }
    },
    type: 'object',
    required: ['workspace_builtin_tool_id', 'success'],
    title: 'AgentBuiltInToolBulkUpdateResult'
} as const;

export const AgentBuiltInToolPublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        },
        display_name: {
            type: 'string',
            title: 'Display Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        required_permission: {
            type: 'boolean',
            title: 'Required Permission'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active'
        }
    },
    type: 'object',
    required: ['id', 'name', 'display_name', 'required_permission', 'is_active'],
    title: 'AgentBuiltInToolPublic'
} as const;

export const AgentBuiltInToolUpdateSchema = {
    properties: {
        workspace_builtin_tool_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Builtin Tool Id'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active'
        }
    },
    type: 'object',
    required: ['workspace_builtin_tool_id', 'is_active'],
    title: 'AgentBuiltInToolUpdate'
} as const;

export const AgentBuiltInToolsBulkUpdateResponseSchema = {
    properties: {
        total_count: {
            type: 'integer',
            title: 'Total Count'
        },
        success_count: {
            type: 'integer',
            title: 'Success Count'
        },
        failed_count: {
            type: 'integer',
            title: 'Failed Count'
        },
        results: {
            items: {
                '$ref': '#/components/schemas/AgentBuiltInToolBulkUpdateResult'
            },
            type: 'array',
            title: 'Results'
        }
    },
    type: 'object',
    required: ['total_count', 'success_count', 'failed_count', 'results'],
    title: 'AgentBuiltInToolsBulkUpdateResponse'
} as const;

export const AgentBuiltInToolsPublicSchema = {
    properties: {
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        tools: {
            items: {
                '$ref': '#/components/schemas/AgentBuiltInToolPublic'
            },
            type: 'array',
            title: 'Tools'
        }
    },
    type: 'object',
    required: ['agent_id', 'tools'],
    title: 'AgentBuiltInToolsPublic'
} as const;

export const AgentBuiltInToolsUpdateRequestSchema = {
    properties: {
        agent_builtin_tools: {
            items: {
                '$ref': '#/components/schemas/AgentBuiltInToolUpdate'
            },
            type: 'array',
            title: 'Agent Builtin Tools'
        }
    },
    type: 'object',
    required: ['agent_builtin_tools'],
    title: 'AgentBuiltInToolsUpdateRequest'
} as const;

export const AgentConnectionCreateRequestSchema = {
    properties: {
        connection_id: {
            type: 'string',
            format: 'uuid',
            title: 'Connection Id'
        }
    },
    type: 'object',
    required: ['connection_id'],
    title: 'AgentConnectionCreateRequest',
    description: 'Request model for creating agent connection'
} as const;

export const AgentConnectionResponseSchema = {
    properties: {
        success: {
            type: 'boolean',
            title: 'Success'
        },
        message: {
            type: 'string',
            title: 'Message'
        }
    },
    type: 'object',
    required: ['success', 'message'],
    title: 'AgentConnectionResponse',
    description: 'Response model for single operations'
} as const;

export const AgentConnectionsPublicSchema = {
    properties: {
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        connections: {
            items: {
                '$ref': '#/components/schemas/ConnectionPublic'
            },
            type: 'array',
            title: 'Connections'
        }
    },
    type: 'object',
    required: ['agent_id', 'connections'],
    title: 'AgentConnectionsPublic',
    description: 'Public schema for agent connections'
} as const;

export const AgentExecutionConfigSchema = {
    properties: {
        message: {
            type: 'string',
            title: 'Message'
        },
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        context_ids: {
            items: {
                type: 'string',
                format: 'uuid'
            },
            type: 'array',
            title: 'Context Ids',
            default: []
        },
        conversation_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Conversation Name'
        }
    },
    type: 'object',
    required: ['message', 'agent_id'],
    title: 'AgentExecutionConfig',
    description: 'Configuration for agent execution'
} as const;

export const AgentInstructionsUpdateSchema = {
    properties: {
        instructions: {
            type: 'string',
            title: 'Instructions'
        }
    },
    type: 'object',
    required: ['instructions'],
    title: 'AgentInstructionsUpdate'
} as const;

export const AgentPublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        alias: {
            type: 'string',
            title: 'Alias'
        },
        title: {
            type: 'string',
            title: 'Title'
        },
        role: {
            type: 'string',
            title: 'Role'
        },
        goal: {
            type: 'string',
            title: 'Goal'
        },
        instructions: {
            type: 'string',
            title: 'Instructions'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active'
        },
        type: {
            '$ref': '#/components/schemas/AgentType'
        }
    },
    type: 'object',
    required: ['id', 'alias', 'title', 'role', 'goal', 'instructions', 'is_active', 'type'],
    title: 'AgentPublic'
} as const;

export const AgentStatusUpdateSchema = {
    properties: {
        agent_status: {
            type: 'boolean',
            title: 'Agent Status'
        }
    },
    type: 'object',
    required: ['agent_status'],
    title: 'AgentStatusUpdate'
} as const;

export const AgentTypeSchema = {
    type: 'string',
    enum: ['conversation_agent', 'autonomous_agent'],
    title: 'AgentType',
    description: 'Defines the supported types of agents in the system.'
} as const;

export const AgentTypeUsageSchema = {
    properties: {
        agent_type: {
            type: 'string',
            title: 'Agent Type'
        },
        total_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Total Tokens'
        }
    },
    type: 'object',
    required: ['agent_type', 'total_tokens'],
    title: 'AgentTypeUsage'
} as const;

export const AgentsBuiltInToolsResponseSchema = {
    properties: {
        agents_builtin_tools: {
            items: {
                '$ref': '#/components/schemas/AgentBuiltInToolsPublic'
            },
            type: 'array',
            title: 'Agents Builtin Tools'
        }
    },
    type: 'object',
    required: ['agents_builtin_tools'],
    title: 'AgentsBuiltInToolsResponse'
} as const;

export const AgentsConnectionsResponseSchema = {
    properties: {
        agents_connections: {
            items: {
                '$ref': '#/components/schemas/AgentConnectionsPublic'
            },
            type: 'array',
            title: 'Agents Connections'
        }
    },
    type: 'object',
    required: ['agents_connections'],
    title: 'AgentsConnectionsResponse',
    description: "Response model for multiple agents' connections"
} as const;

export const AgentsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/AgentPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'AgentsPublic'
} as const;

export const AlertCreateSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 200,
            title: 'Title',
            description: 'Alert title'
        },
        description: {
            type: 'string',
            title: 'Description',
            description: 'Detailed alert description'
        },
        severity: {
            '$ref': '#/components/schemas/AlertSeverity',
            description: 'Alert severity level'
        }
    },
    type: 'object',
    required: ['title', 'description', 'severity'],
    title: 'AlertCreate',
    description: 'Schema for creating a new alert'
} as const;

export const AlertListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/AlertResponse'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['data', 'total'],
    title: 'AlertList',
    description: 'Schema for list of alerts with pagination'
} as const;

export const AlertResponseSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 200,
            title: 'Title',
            description: 'Alert title'
        },
        description: {
            type: 'string',
            title: 'Description',
            description: 'Detailed alert description'
        },
        severity: {
            '$ref': '#/components/schemas/AlertSeverity',
            description: 'Alert severity level'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        status: {
            '$ref': '#/components/schemas/AlertStatus'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['title', 'description', 'severity', 'id', 'workspace_id', 'status', 'created_at'],
    title: 'AlertResponse',
    description: 'Schema for alert response'
} as const;

export const AlertSeveritySchema = {
    type: 'string',
    enum: ['CRITICAL', 'HIGH', 'MEDIUM', 'LOW', 'INFO'],
    title: 'AlertSeverity'
} as const;

export const AlertStatusSchema = {
    type: 'string',
    enum: ['OPEN', 'ACKNOWLEDGED', 'RESOLVED', 'CLOSED'],
    title: 'AlertStatus'
} as const;

export const AlertStatusSummarySchema = {
    properties: {
        status_counts: {
            additionalProperties: {
                type: 'integer'
            },
            type: 'object',
            title: 'Status Counts',
            description: 'Count of alerts by status'
        },
        total: {
            type: 'integer',
            title: 'Total',
            description: 'Total number of alerts in the period'
        }
    },
    type: 'object',
    required: ['status_counts', 'total'],
    title: 'AlertStatusSummary',
    description: 'Summary of alerts by status for the last 30 days'
} as const;

export const AlertUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 200
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        severity: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/AlertSeverity'
                },
                {
                    type: 'null'
                }
            ]
        },
        status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/AlertStatus'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    title: 'AlertUpdate',
    description: 'Schema for updating an existing alert'
} as const;

export const AllConstantsResponseSchema = {
    properties: {
        constants: {
            additionalProperties: {
                '$ref': '#/components/schemas/ConstantCategory'
            },
            type: 'object',
            title: 'Constants'
        },
        total_categories: {
            type: 'integer',
            title: 'Total Categories'
        },
        generated_at: {
            type: 'string',
            title: 'Generated At'
        }
    },
    type: 'object',
    required: ['constants', 'total_categories', 'generated_at'],
    title: 'AllConstantsResponse',
    description: 'Schema for returning all constants at once.'
} as const;

export const AsyncTaskStatusSchema = {
    type: 'string',
    enum: ['PENDING', 'PROGRESS', 'SUCCESS', 'FAILURE'],
    title: 'AsyncTaskStatus'
} as const;

export const AttachmentConfirmRequestSchema = {
    properties: {
        uploaded_files: {
            items: {
                '$ref': '#/components/schemas/UploadedAttachmentInfo'
            },
            type: 'array',
            title: 'Uploaded Files',
            description: 'List of files that have been successfully uploaded.'
        }
    },
    type: 'object',
    required: ['uploaded_files'],
    title: 'AttachmentConfirmRequest'
} as const;

export const AttachmentDownloadResponseSchema = {
    properties: {
        attachment_id: {
            type: 'string',
            format: 'uuid',
            title: 'Attachment Id',
            description: 'The ID of the attachment.'
        },
        download_url: {
            type: 'string',
            title: 'Download Url',
            description: 'The presigned URL for downloading the file.'
        }
    },
    type: 'object',
    required: ['attachment_id', 'download_url'],
    title: 'AttachmentDownloadResponse'
} as const;

export const AttachmentFileInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side unique ID for the file.'
        },
        filename: {
            type: 'string',
            maxLength: 255,
            title: 'Filename',
            description: 'Original name of the file.'
        },
        content_type: {
            type: 'string',
            maxLength: 255,
            title: 'Content Type',
            description: 'MIME type of the file.'
        },
        file_size: {
            type: 'integer',
            maximum: 26214400,
            exclusiveMinimum: 0,
            title: 'File Size',
            description: 'Size of the file in bytes.'
        }
    },
    type: 'object',
    required: ['file_id', 'filename', 'content_type', 'file_size'],
    title: 'AttachmentFileInfo'
} as const;

export const AttachmentMetadataResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id',
            description: 'The ID of the attachment.'
        },
        filename: {
            type: 'string',
            title: 'Filename',
            description: 'The name of the file.'
        },
        original_filename: {
            type: 'string',
            title: 'Original Filename',
            description: 'The original name of the file.'
        },
        file_type: {
            type: 'string',
            title: 'File Type',
            description: 'The MIME type of the file.'
        },
        file_size: {
            type: 'integer',
            title: 'File Size',
            description: 'The size of the file in bytes.'
        },
        storage_key: {
            type: 'string',
            title: 'Storage Key',
            description: 'The storage key of the file.'
        },
        created_at: {
            type: 'string',
            title: 'Created At',
            description: 'The creation timestamp of the attachment.'
        }
    },
    type: 'object',
    required: ['id', 'filename', 'original_filename', 'file_type', 'file_size', 'storage_key', 'created_at'],
    title: 'AttachmentMetadataResponse'
} as const;

export const AttachmentPresignedUrlRequestSchema = {
    properties: {
        files: {
            items: {
                '$ref': '#/components/schemas/AttachmentFileInfo'
            },
            type: 'array',
            title: 'Files',
            description: 'List of files to generate presigned URLs for.'
        }
    },
    type: 'object',
    required: ['files'],
    title: 'AttachmentPresignedUrlRequest'
} as const;

export const AttachmentPresignedUrlResponseSchema = {
    properties: {
        presigned_urls: {
            items: {
                '$ref': '#/components/schemas/app__schemas__message_attachment__PresignedUrlInfo'
            },
            type: 'array',
            title: 'Presigned Urls',
            description: 'List of presigned URLs and associated file info.'
        }
    },
    type: 'object',
    required: ['presigned_urls'],
    title: 'AttachmentPresignedUrlResponse'
} as const;

export const AvailableUserSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        email: {
            type: 'string',
            title: 'Email'
        },
        full_name: {
            type: 'string',
            title: 'Full Name'
        }
    },
    type: 'object',
    required: ['id', 'email', 'full_name'],
    title: 'AvailableUser'
} as const;

export const AvailableUsersCurrentWorkspaceSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/AvailableUser'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'AvailableUsersCurrentWorkspace'
} as const;

export const AzureOnboardingCreateSchema = {
    properties: {
        app_id: {
            type: 'string',
            title: 'App Id'
        },
        client_secret: {
            type: 'string',
            title: 'Client Secret'
        },
        tenant: {
            type: 'string',
            title: 'Tenant'
        }
    },
    type: 'object',
    required: ['app_id', 'client_secret', 'tenant'],
    title: 'AzureOnboardingCreate'
} as const;

export const BillingDetailsSchema = {
    properties: {
        address: {
            '$ref': '#/components/schemas/Address'
        },
        email: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Email'
        },
        name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        phone: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Phone'
        }
    },
    type: 'object',
    required: ['address'],
    title: 'BillingDetails'
} as const;

export const Body_login_login_access_tokenSchema = {
    properties: {
        grant_type: {
            type: 'string',
            pattern: 'password',
            title: 'Grant Type'
        },
        username: {
            type: 'string',
            title: 'Username'
        },
        password: {
            type: 'string',
            title: 'Password'
        },
        scope: {
            type: 'string',
            title: 'Scope',
            default: ''
        },
        client_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Client Id'
        },
        client_secret: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Client Secret'
        },
        slackOAuth: {
            type: 'boolean',
            title: 'Slackoauth',
            default: false
        },
        appId: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Appid'
        },
        teamId: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Teamid'
        }
    },
    type: 'object',
    required: ['username', 'password'],
    title: 'Body_login-login_access_token'
} as const;

export const Body_notifications_list_notificationsSchema = {
    properties: {
        type: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/NotificationType'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Type'
        },
        status: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/NotificationStatus'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Status'
        }
    },
    type: 'object',
    title: 'Body_notifications-list_notifications'
} as const;

export const BuiltInToolPublicSchema = {
    properties: {
        name: {
            type: 'string',
            title: 'Name'
        },
        display_name: {
            type: 'string',
            title: 'Display Name'
        },
        description: {
            type: 'string',
            title: 'Description'
        }
    },
    type: 'object',
    required: ['name', 'display_name', 'description'],
    title: 'BuiltInToolPublic'
} as const;

export const BuiltInToolUpdateRequestSchema = {
    properties: {
        required_permission: {
            type: 'boolean',
            title: 'Required Permission'
        }
    },
    type: 'object',
    required: ['required_permission'],
    title: 'BuiltInToolUpdateRequest'
} as const;

export const BuiltinInstallRequestSchema = {
    properties: {
        config_override: {
            anyOf: [
                {
                    type: 'object'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Config Override',
            description: 'Configuration override for the builtin connection (e.g., environment variables)'
        }
    },
    type: 'object',
    title: 'BuiltinInstallRequest',
    description: 'Request model for installing builtin connections with custom configuration'
} as const;

export const CardDetailsSchema = {
    properties: {
        brand: {
            type: 'string',
            title: 'Brand'
        },
        country: {
            type: 'string',
            title: 'Country'
        },
        display_brand: {
            type: 'string',
            title: 'Display Brand'
        },
        exp_month: {
            type: 'integer',
            title: 'Exp Month'
        },
        exp_year: {
            type: 'integer',
            title: 'Exp Year'
        },
        last4: {
            type: 'string',
            title: 'Last4'
        }
    },
    type: 'object',
    required: ['brand', 'country', 'display_brand', 'exp_month', 'exp_year', 'last4'],
    title: 'CardDetails'
} as const;

export const ChartTypeSchema = {
    type: 'string',
    enum: ['line', 'bar', 'pie', 'doughnut', 'area', 'scatter', 'radar', 'step_area', 'sankey'],
    title: 'ChartType',
    description: 'Enum for different types of charts available in the system'
} as const;

export const CheckoutSessionResponseSchema = {
    properties: {
        checkout_session_url: {
            type: 'string',
            title: 'Checkout Session Url'
        }
    },
    type: 'object',
    required: ['checkout_session_url'],
    title: 'CheckoutSessionResponse'
} as const;

export const CloudProviderSchema = {
    type: 'string',
    enum: ['AWS', 'GCP', 'AZURE'],
    title: 'CloudProvider'
} as const;

export const CloudSyncConfigCreateSchema = {
    properties: {
        include_stopped_resources: {
            type: 'boolean',
            title: 'Include Stopped Resources',
            default: false
        },
        refresh_interval: {
            type: 'integer',
            maximum: 10080,
            minimum: 1,
            title: 'Refresh Interval',
            default: 60
        },
        selected_resources: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Selected Resources',
            default: []
        },
        is_enabled: {
            type: 'boolean',
            title: 'Is Enabled',
            default: true
        },
        connection_id: {
            type: 'string',
            format: 'uuid',
            title: 'Connection Id'
        }
    },
    type: 'object',
    required: ['connection_id'],
    title: 'CloudSyncConfigCreate'
} as const;

export const CloudSyncConfigPublicSchema = {
    properties: {
        include_stopped_resources: {
            type: 'boolean',
            title: 'Include Stopped Resources',
            default: false
        },
        refresh_interval: {
            type: 'integer',
            maximum: 10080,
            minimum: 1,
            title: 'Refresh Interval',
            default: 60
        },
        selected_resources: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Selected Resources',
            default: []
        },
        is_enabled: {
            type: 'boolean',
            title: 'Is Enabled',
            default: true
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        connection_id: {
            type: 'string',
            format: 'uuid',
            title: 'Connection Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        last_sync_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Sync At'
        }
    },
    type: 'object',
    required: ['id', 'workspace_id', 'connection_id', 'created_at', 'updated_at'],
    title: 'CloudSyncConfigPublic'
} as const;

export const CloudSyncConfigUpdateSchema = {
    properties: {
        include_stopped_resources: {
            anyOf: [
                {
                    type: 'boolean'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Include Stopped Resources'
        },
        refresh_interval: {
            anyOf: [
                {
                    type: 'integer',
                    maximum: 10080,
                    minimum: 1
                },
                {
                    type: 'null'
                }
            ],
            title: 'Refresh Interval'
        },
        selected_resources: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Selected Resources'
        },
        is_enabled: {
            anyOf: [
                {
                    type: 'boolean'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Is Enabled'
        }
    },
    type: 'object',
    title: 'CloudSyncConfigUpdate'
} as const;

export const ConfirmUploadsRequestSchema = {
    properties: {
        uploaded_files: {
            items: {
                '$ref': '#/components/schemas/UploadedFileInfo'
            },
            type: 'array',
            title: 'Uploaded Files',
            description: 'Information about successfully uploaded files'
        }
    },
    type: 'object',
    required: ['uploaded_files'],
    title: 'ConfirmUploadsRequest',
    description: 'Request to confirm file uploads and start ingestion'
} as const;

export const ConnectionCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            title: 'Name'
        },
        prefix: {
            type: 'string',
            maxLength: 255,
            title: 'Prefix'
        },
        type: {
            '$ref': '#/components/schemas/ConnectionType',
            default: 'mcp'
        },
        transport_type: {
            '$ref': '#/components/schemas/ConnectionTransport',
            default: 'streamable_http'
        },
        config: {
            type: 'object',
            title: 'Config',
            default: {}
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: true
        },
        tool_list: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool List',
            default: []
        },
        tool_permissions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool Permissions',
            default: []
        },
        tool_enabled: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool Enabled',
            default: []
        },
        tool_schemas: {
            items: {
                type: 'object'
            },
            type: 'array',
            title: 'Tool Schemas',
            default: []
        },
        status: {
            '$ref': '#/components/schemas/ConnectionStatus',
            default: 'connected'
        },
        status_message: {
            type: 'string',
            title: 'Status Message',
            default: ''
        },
        status_updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Status Updated At',
            default: '2025-07-30T10:20:25.927881'
        }
    },
    type: 'object',
    required: ['name', 'prefix'],
    title: 'ConnectionCreate'
} as const;

export const ConnectionPublicSchema = {
    properties: {
        name: {
            type: 'string',
            title: 'Name'
        },
        prefix: {
            type: 'string',
            title: 'Prefix'
        },
        type: {
            '$ref': '#/components/schemas/ConnectionType'
        },
        transport_type: {
            '$ref': '#/components/schemas/ConnectionTransport'
        },
        config: {
            type: 'object',
            title: 'Config'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active'
        },
        tool_list: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool List'
        },
        tool_permissions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool Permissions'
        },
        tool_enabled: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tool Enabled'
        },
        status: {
            '$ref': '#/components/schemas/ConnectionStatus'
        },
        status_message: {
            type: 'string',
            title: 'Status Message'
        },
        status_updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Status Updated At'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Workspace Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['name', 'prefix', 'type', 'transport_type', 'config', 'is_active', 'tool_list', 'tool_permissions', 'tool_enabled', 'status', 'status_message', 'status_updated_at', 'id', 'workspace_id', 'created_at', 'updated_at'],
    title: 'ConnectionPublic'
} as const;

export const ConnectionStatusSchema = {
    type: 'string',
    enum: ['connected', 'error'],
    title: 'ConnectionStatus'
} as const;

export const ConnectionTransportSchema = {
    type: 'string',
    enum: ['streamable_http', 'sse'],
    title: 'ConnectionTransport'
} as const;

export const ConnectionTypeSchema = {
    type: 'string',
    enum: ['builtin', 'mcp', 'cloud', 'cli'],
    title: 'ConnectionType'
} as const;

export const ConnectionUpdateSchema = {
    properties: {
        name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Name'
        },
        prefix: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Prefix'
        },
        type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/ConnectionType'
                },
                {
                    type: 'null'
                }
            ]
        },
        transport_type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/ConnectionTransport'
                },
                {
                    type: 'null'
                }
            ]
        },
        config: {
            anyOf: [
                {
                    type: 'object'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Config'
        },
        is_active: {
            anyOf: [
                {
                    type: 'boolean'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Is Active'
        },
        tool_list: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tool List'
        },
        tool_permissions: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tool Permissions'
        },
        tool_enabled: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tool Enabled'
        },
        tool_schemas: {
            anyOf: [
                {
                    items: {
                        type: 'object'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tool Schemas'
        },
        status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/ConnectionStatus'
                },
                {
                    type: 'null'
                }
            ]
        },
        status_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Status Message'
        },
        status_updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Status Updated At'
        }
    },
    type: 'object',
    title: 'ConnectionUpdate'
} as const;

export const ConnectionsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/ConnectionPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'ConnectionsPublic'
} as const;

export const ConstantCategoriesResponseSchema = {
    properties: {
        categories: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Categories'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['categories', 'total'],
    title: 'ConstantCategoriesResponse',
    description: 'Schema for listing all available constant categories.'
} as const;

export const ConstantCategorySchema = {
    properties: {
        category: {
            type: 'string',
            title: 'Category'
        },
        data: {
            items: {
                '$ref': '#/components/schemas/ConstantOption'
            },
            type: 'array',
            title: 'Data'
        },
        description: {
            type: 'string',
            title: 'Description'
        },
        total: {
            type: 'integer',
            title: 'Total'
        },
        last_updated: {
            type: 'string',
            title: 'Last Updated'
        }
    },
    type: 'object',
    required: ['category', 'data', 'description', 'total', 'last_updated'],
    title: 'ConstantCategory',
    description: 'Schema for a constant category response.'
} as const;

export const ConstantOptionSchema = {
    properties: {
        value: {
            type: 'string',
            title: 'Value'
        },
        label: {
            type: 'string',
            title: 'Label'
        },
        key: {
            type: 'string',
            title: 'Key'
        },
        metadata: {
            anyOf: [
                {
                    type: 'object'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Metadata'
        }
    },
    type: 'object',
    required: ['value', 'label', 'key'],
    title: 'ConstantOption',
    description: 'Schema for a single constant option.'
} as const;

export const ConversationCreateRequestSchema = {
    properties: {
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        resource_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Resource Id'
        }
    },
    type: 'object',
    required: ['agent_id'],
    title: 'ConversationCreateRequest'
} as const;

export const ConversationPublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        agent_id: {
            type: 'string',
            format: 'uuid',
            title: 'Agent Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        },
        resource: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/ResourcePublicSimple'
                },
                {
                    type: 'null'
                }
            ]
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['id', 'agent_id', 'name', 'created_at'],
    title: 'ConversationPublic'
} as const;

export const ConversationRenameRequestSchema = {
    properties: {
        name: {
            type: 'string',
            title: 'Name'
        }
    },
    type: 'object',
    required: ['name'],
    title: 'ConversationRenameRequest'
} as const;

export const ConversationsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/ConversationPublic'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['data', 'total'],
    title: 'ConversationsPublic',
    description: 'Response model for paginated conversations list.'
} as const;

export const DailyMessageVolumeSchema = {
    properties: {
        date: {
            type: 'string',
            format: 'date-time',
            title: 'Date'
        },
        message_count: {
            type: 'integer',
            title: 'Message Count'
        }
    },
    type: 'object',
    required: ['date', 'message_count'],
    title: 'DailyMessageVolume'
} as const;

export const DailyTokenUsageSchema = {
    properties: {
        date: {
            type: 'string',
            format: 'date-time',
            title: 'Date'
        },
        total_tokens: {
            type: 'integer',
            title: 'Tokens'
        }
    },
    type: 'object',
    required: ['date', 'total_tokens'],
    title: 'DailyTokenUsage'
} as const;

export const DashboardSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        title: {
            type: 'string',
            title: 'Title'
        },
        grid_config: {
            type: 'object',
            title: 'Grid Config',
            default: {
                columns: 12
            }
        },
        widgets: {
            items: {},
            type: 'array',
            title: 'Widgets',
            default: []
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['conversation_id', 'workspace_id', 'title'],
    title: 'Dashboard'
} as const;

export const DocumentKBReadSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 0,
            title: 'Name'
        },
        type: {
            '$ref': '#/components/schemas/DocumentType'
        },
        url: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Url'
        },
        deep_crawl: {
            type: 'boolean',
            title: 'Deep Crawl',
            default: false
        },
        file_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'File Name'
        },
        file_type: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'File Type'
        },
        object_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Object Name'
        },
        embed_status: {
            '$ref': '#/components/schemas/AsyncTaskStatus',
            default: 'PENDING'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        kb_id: {
            type: 'string',
            format: 'uuid',
            title: 'Kb Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted'
        },
        parent_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Parent Id'
        },
        children: {
            items: {
                '$ref': '#/components/schemas/DocumentKBRead'
            },
            type: 'array',
            title: 'Children',
            default: []
        }
    },
    type: 'object',
    required: ['name', 'type', 'id', 'kb_id', 'created_at', 'updated_at', 'is_deleted'],
    title: 'DocumentKBRead'
} as const;

export const DocumentTypeSchema = {
    type: 'string',
    enum: ['url', 'file'],
    title: 'DocumentType'
} as const;

export const DocumentsKBReadSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/DocumentKBRead'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'DocumentsKBRead'
} as const;

export const EnterpriseEnquiryMessageResponseSchema = {
    properties: {
        message: {
            type: 'string',
            title: 'Message'
        }
    },
    type: 'object',
    required: ['message'],
    title: 'EnterpriseEnquiryMessageResponse',
    description: 'Response model for enterprise enquiry status messages'
} as const;

export const EnterpriseEnquiryRequestSchema = {
    properties: {
        first_name: {
            type: 'string',
            title: 'First Name'
        },
        last_name: {
            type: 'string',
            title: 'Last Name'
        },
        work_title: {
            type: 'string',
            title: 'Work Title'
        },
        work_email: {
            type: 'string',
            title: 'Work Email'
        },
        company_name: {
            type: 'string',
            title: 'Company Name'
        },
        estimated_monthly_cost: {
            type: 'string',
            title: 'Estimated Monthly Cost'
        },
        message: {
            type: 'string',
            title: 'Message'
        },
        product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Product Id'
        }
    },
    type: 'object',
    required: ['first_name', 'last_name', 'work_title', 'work_email', 'company_name', 'estimated_monthly_cost', 'message', 'product_id'],
    title: 'EnterpriseEnquiryRequest'
} as const;

export const FeedbackTypeSchema = {
    type: 'string',
    enum: ['good', 'bad'],
    title: 'FeedbackType',
    description: 'Enumeration for feedback types on agent responses.'
} as const;

export const FileContentResponseSchema = {
    properties: {
        content: {
            type: 'string',
            title: 'Content',
            description: 'File content'
        },
        path: {
            type: 'string',
            title: 'Path',
            description: 'File path'
        },
        name: {
            type: 'string',
            title: 'Name',
            description: 'File name'
        }
    },
    type: 'object',
    required: ['content', 'path', 'name'],
    title: 'FileContentResponse',
    description: 'Response for file content requests'
} as const;

export const FileInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side ID for tracking this file'
        },
        filename: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Filename',
            description: 'Original filename'
        },
        content_type: {
            type: 'string',
            title: 'Content Type',
            description: 'File MIME type'
        },
        file_size: {
            type: 'integer',
            maximum: 10485760,
            exclusiveMinimum: 0,
            title: 'File Size',
            description: 'File size in bytes'
        }
    },
    type: 'object',
    required: ['file_id', 'filename', 'content_type', 'file_size'],
    title: 'FileInfo',
    description: 'Information about a file to generate a presigned URL for'
} as const;

export const FileListResponseSchema = {
    properties: {
        files: {
            items: {
                '$ref': '#/components/schemas/FileNode'
            },
            type: 'array',
            title: 'Files',
            description: 'List of files and directories'
        },
        current_path: {
            type: 'string',
            title: 'Current Path',
            description: 'Current directory path'
        }
    },
    type: 'object',
    required: ['files', 'current_path'],
    title: 'FileListResponse',
    description: 'Response for directory listing'
} as const;

export const FileNodeSchema = {
    properties: {
        id: {
            type: 'string',
            title: 'Id',
            description: 'Unique identifier for the file/directory'
        },
        name: {
            type: 'string',
            title: 'Name',
            description: 'File or directory name'
        },
        path: {
            type: 'string',
            title: 'Path',
            description: 'Full path'
        },
        type: {
            '$ref': '#/components/schemas/FileType',
            description: 'File type (file or directory)'
        },
        children: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/FileNode'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Children',
            description: 'Child nodes for directories'
        },
        content: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Content',
            description: 'File content for files'
        }
    },
    type: 'object',
    required: ['id', 'name', 'path', 'type'],
    title: 'FileNode',
    description: 'Simplified file node matching frontend interface'
} as const;

export const FileTypeSchema = {
    type: 'string',
    enum: ['file', 'directory'],
    title: 'FileType'
} as const;

export const GCPAccountCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        key: {
            type: 'string',
            maxLength: 4096,
            title: 'Key'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'key'],
    title: 'GCPAccountCreate'
} as const;

export const GCPAccountDetailSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        key: {
            type: 'string',
            title: 'Key'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'id', 'key'],
    title: 'GCPAccountDetail'
} as const;

export const GCPAccountPublicSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'id'],
    title: 'GCPAccountPublic'
} as const;

export const GCPAccountUpdateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        environment: {
            '$ref': '#/components/schemas/AccountEnvironement'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        key: {
            type: 'string',
            maxLength: 4096,
            title: 'Key'
        }
    },
    type: 'object',
    required: ['name', 'environment', 'workspace_id', 'key'],
    title: 'GCPAccountUpdate'
} as const;

export const GCPOnboardingCreateSchema = {
    properties: {
        google_service_account_key: {
            type: 'string',
            title: 'Google Service Account Key'
        },
        provider_id: {
            type: 'string',
            title: 'Provider Id'
        }
    },
    type: 'object',
    required: ['google_service_account_key', 'provider_id'],
    title: 'GCPOnboardingCreate'
} as const;

export const HTTPValidationErrorSchema = {
    properties: {
        detail: {
            items: {
                '$ref': '#/components/schemas/ValidationError'
            },
            type: 'array',
            title: 'Detail'
        }
    },
    type: 'object',
    title: 'HTTPValidationError'
} as const;

export const InvoiceResponseSchema = {
    properties: {
        id: {
            type: 'string',
            title: 'Id'
        },
        customer: {
            type: 'string',
            title: 'Customer'
        },
        status: {
            type: 'string',
            title: 'Status'
        },
        amount_due: {
            type: 'integer',
            title: 'Amount Due'
        },
        amount_paid: {
            type: 'integer',
            title: 'Amount Paid'
        },
        amount_remaining: {
            type: 'integer',
            title: 'Amount Remaining'
        },
        currency: {
            type: 'string',
            title: 'Currency'
        },
        invoice_pdf: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Invoice Pdf'
        },
        created: {
            type: 'integer',
            title: 'Created'
        },
        due_date: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Due Date'
        },
        paid: {
            type: 'boolean',
            title: 'Paid'
        },
        payment_intent: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Payment Intent'
        },
        subscription: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Subscription'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['id', 'customer', 'status', 'amount_due', 'amount_paid', 'amount_remaining', 'currency', 'created', 'paid', 'total'],
    title: 'InvoiceResponse'
} as const;

export const KBAccessLevelSchema = {
    type: 'string',
    enum: ['private', 'shared'],
    title: 'KBAccessLevel'
} as const;

export const KBCreateSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        access_level: {
            '$ref': '#/components/schemas/KBAccessLevel',
            default: 'private'
        },
        usage_mode: {
            '$ref': '#/components/schemas/KBUsageMode',
            default: 'manual'
        },
        tags: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tags',
            default: []
        },
        allowed_users: {
            items: {
                type: 'string',
                format: 'uuid'
            },
            type: 'array',
            title: 'Allowed Users',
            default: []
        }
    },
    type: 'object',
    required: ['title'],
    title: 'KBCreate'
} as const;

export const KBReadSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        access_level: {
            '$ref': '#/components/schemas/KBAccessLevel',
            default: 'private'
        },
        usage_mode: {
            '$ref': '#/components/schemas/KBUsageMode',
            default: 'manual'
        },
        tags: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tags',
            default: []
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted'
        },
        allowed_users: {
            items: {
                type: 'string',
                format: 'uuid'
            },
            type: 'array',
            title: 'Allowed Users'
        },
        owner_id: {
            type: 'string',
            format: 'uuid',
            title: 'Owner Id'
        }
    },
    type: 'object',
    required: ['title', 'id', 'created_at', 'updated_at', 'is_deleted', 'owner_id'],
    title: 'KBRead'
} as const;

export const KBUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        access_level: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/KBAccessLevel'
                },
                {
                    type: 'null'
                }
            ]
        },
        tags: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Tags'
        },
        allowed_users: {
            anyOf: [
                {
                    items: {
                        type: 'string',
                        format: 'uuid'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Allowed Users'
        },
        usage_mode: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/KBUsageMode'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    title: 'KBUpdate'
} as const;

export const KBUsageModeSchema = {
    type: 'string',
    enum: ['manual', 'agent_requested', 'always'],
    title: 'KBUsageMode'
} as const;

export const KBsReadSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/KBRead'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'KBsRead'
} as const;

export const MessageSchema = {
    properties: {
        content: {
            type: 'string',
            title: 'Content'
        },
        role: {
            type: 'string',
            maxLength: 50,
            title: 'Role',
            default: 'user'
        },
        is_interrupt: {
            type: 'boolean',
            title: 'Is Interrupt',
            default: false
        },
        interrupt_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Interrupt Message'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        message_metadata: {
            type: 'object',
            title: 'Message Metadata',
            default: {}
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted',
            default: false
        }
    },
    type: 'object',
    required: ['content', 'conversation_id'],
    title: 'Message'
} as const;

export const MessageAgentThoughtPublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        position: {
            type: 'integer',
            title: 'Position'
        },
        tool_name: {
            type: 'string',
            title: 'Tool Name'
        },
        tool_input: {
            type: 'object',
            title: 'Tool Input'
        },
        tool_output: {
            type: 'string',
            title: 'Tool Output'
        },
        tool_runtime: {
            type: 'number',
            title: 'Tool Runtime',
            default: 0
        },
        content: {
            type: 'string',
            title: 'Content'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['id', 'position', 'tool_name', 'tool_input', 'tool_output', 'content', 'created_at'],
    title: 'MessageAgentThoughtPublic'
} as const;

export const MessageAttachmentPublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        filename: {
            type: 'string',
            title: 'Filename'
        },
        original_filename: {
            type: 'string',
            title: 'Original Filename'
        },
        file_type: {
            type: 'string',
            title: 'File Type'
        },
        file_size: {
            type: 'integer',
            title: 'File Size'
        },
        storage_key: {
            type: 'string',
            title: 'Storage Key'
        },
        thumbnail_key: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Thumbnail Key'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['id', 'filename', 'original_filename', 'file_type', 'file_size', 'storage_key', 'created_at'],
    title: 'MessageAttachmentPublic'
} as const;

export const MessageDisplayComponentPublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        type: {
            '$ref': '#/components/schemas/MessageDisplayComponentType'
        },
        chart_type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/ChartType'
                },
                {
                    type: 'null'
                }
            ]
        },
        title: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        data: {
            type: 'object',
            title: 'Data'
        },
        config: {
            type: 'object',
            title: 'Config'
        },
        position: {
            type: 'integer',
            title: 'Position'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['id', 'type', 'chart_type', 'title', 'description', 'data', 'config', 'position', 'created_at'],
    title: 'MessageDisplayComponentPublic',
    description: 'Public schema for message display components'
} as const;

export const MessageDisplayComponentTypeSchema = {
    type: 'string',
    enum: ['table', 'chart'],
    title: 'MessageDisplayComponentType',
    description: 'Enum for display component types (currently supporting only tables and charts)'
} as const;

export const MessageFeedbackCreateSchema = {
    properties: {
        feedback_type: {
            '$ref': '#/components/schemas/FeedbackType',
            description: 'Type of feedback (good/bad)'
        },
        reason: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Reason',
            description: 'Optional reason for the feedback, required when feedback_type is BAD'
        },
        additional_comments: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 2000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Additional Comments',
            description: 'Additional optional comments from the user'
        },
        message_id: {
            type: 'string',
            format: 'uuid',
            title: 'Message Id'
        }
    },
    type: 'object',
    required: ['feedback_type', 'message_id'],
    title: 'MessageFeedbackCreate',
    description: 'Schema for creating message feedback'
} as const;

export const MessageFeedbackPublicSchema = {
    properties: {
        feedback_type: {
            '$ref': '#/components/schemas/FeedbackType',
            description: 'Type of feedback (good/bad)'
        },
        reason: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Reason',
            description: 'Optional reason for the feedback, required when feedback_type is BAD'
        },
        additional_comments: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 2000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Additional Comments',
            description: 'Additional optional comments from the user'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        message_id: {
            type: 'string',
            format: 'uuid',
            title: 'Message Id'
        },
        user_id: {
            type: 'string',
            format: 'uuid',
            title: 'User Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['feedback_type', 'id', 'message_id', 'user_id', 'created_at', 'updated_at'],
    title: 'MessageFeedbackPublic',
    description: 'Public schema for message feedback responses'
} as const;

export const MessageFeedbackUpdateSchema = {
    properties: {
        feedback_type: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/FeedbackType'
                },
                {
                    type: 'null'
                }
            ]
        },
        reason: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Reason'
        },
        additional_comments: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Additional Comments'
        }
    },
    type: 'object',
    title: 'MessageFeedbackUpdate',
    description: 'Schema for updating message feedback'
} as const;

export const MessagePublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        content: {
            type: 'string',
            title: 'Content'
        },
        role: {
            type: 'string',
            title: 'Role'
        },
        is_interrupt: {
            type: 'boolean',
            title: 'Is Interrupt'
        },
        interrupt_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Interrupt Message'
        },
        thoughts: {
            items: {
                '$ref': '#/components/schemas/MessageAgentThoughtPublic'
            },
            type: 'array',
            title: 'Thoughts'
        },
        display_components: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/MessageDisplayComponentPublic'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Display Components'
        },
        attachments: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/MessageAttachmentPublic'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Attachments'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        }
    },
    type: 'object',
    required: ['id', 'content', 'role', 'is_interrupt', 'thoughts', 'created_at'],
    title: 'MessagePublic'
} as const;

export const MessagePublicListSchema = {
    properties: {
        messages: {
            items: {
                '$ref': '#/components/schemas/MessagePublic'
            },
            type: 'array',
            title: 'Messages'
        },
        resource_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Resource Id'
        },
        has_report: {
            type: 'boolean',
            title: 'Has Report',
            default: false
        },
        has_dashboard: {
            type: 'boolean',
            title: 'Has Dashboard',
            default: false
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['messages', 'total'],
    title: 'MessagePublicList'
} as const;

export const MessageResponseSchema = {
    properties: {
        message: {
            type: 'string',
            title: 'Message'
        }
    },
    type: 'object',
    required: ['message'],
    title: 'MessageResponse'
} as const;

export const MessageStatisticsSchema = {
    properties: {
        total_messages: {
            type: 'integer',
            title: 'Total Messages'
        },
        average_response_time: {
            type: 'number',
            title: 'Average Response Time'
        },
        average_input_tokens_per_message: {
            type: 'number',
            title: 'Average Input Tokens per Message'
        },
        average_output_tokens_per_message: {
            type: 'number',
            title: 'Average Output Tokens per Message'
        },
        daily_message_volume: {
            items: {
                '$ref': '#/components/schemas/DailyMessageVolume'
            },
            type: 'array',
            title: 'Daily Message Volume'
        },
        token_distribution_by_message_length: {
            items: {
                '$ref': '#/components/schemas/TokenDistributionCategory'
            },
            type: 'array',
            title: 'Token Distribution by Message Length'
        }
    },
    type: 'object',
    required: ['total_messages', 'average_response_time', 'average_input_tokens_per_message', 'average_output_tokens_per_message', 'daily_message_volume', 'token_distribution_by_message_length'],
    title: 'MessageStatistics'
} as const;

export const MessageStreamInputSchema = {
    properties: {
        content: {
            type: 'string',
            title: 'Content'
        },
        resume: {
            type: 'boolean',
            title: 'Resume'
        },
        approve: {
            type: 'boolean',
            title: 'Approve'
        },
        display_components: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/MessageDisplayComponentPublic'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Display Components'
        },
        attachment_ids: {
            anyOf: [
                {
                    items: {
                        type: 'string',
                        format: 'uuid'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Attachment Ids'
        },
        resource_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Resource Id'
        }
    },
    type: 'object',
    required: ['content', 'resume', 'approve'],
    title: 'MessageStreamInput'
} as const;

export const ModuleSettingSchema = {
    properties: {
        key: {
            type: 'string',
            title: 'Key'
        },
        value: {
            type: 'object',
            title: 'Value',
            default: {}
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['key'],
    title: 'ModuleSetting'
} as const;

export const NewPasswordSchema = {
    properties: {
        token: {
            type: 'string',
            title: 'Token'
        },
        new_password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'New Password'
        }
    },
    type: 'object',
    required: ['token', 'new_password'],
    title: 'NewPassword'
} as const;

export const NotificationListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/NotificationResponse'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'NotificationList',
    description: `Response model for paginated notifications list.

Attributes:
    data: List of notification items
    count: Total number of items available (before pagination)`
} as const;

export const NotificationResponseSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            title: 'Title'
        },
        message: {
            type: 'string',
            title: 'Message'
        },
        type: {
            '$ref': '#/components/schemas/NotificationType',
            default: 'info'
        },
        status: {
            '$ref': '#/components/schemas/NotificationStatus',
            default: 'unread'
        },
        notification_metadata: {
            type: 'object',
            title: 'Notification Metadata',
            description: 'Metadata for the notification'
        },
        requires_action: {
            type: 'boolean',
            title: 'Requires Action',
            default: false
        },
        action_url: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Action Url',
            description: 'URL for direct action'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        user_id: {
            type: 'string',
            format: 'uuid',
            title: 'User Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        read_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Read At'
        },
        expires_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Expires At'
        }
    },
    type: 'object',
    required: ['title', 'message', 'id', 'user_id', 'created_at', 'updated_at'],
    title: 'NotificationResponse'
} as const;

export const NotificationStatusSchema = {
    type: 'string',
    enum: ['unread', 'read', 'archived'],
    title: 'NotificationStatus'
} as const;

export const NotificationTypeSchema = {
    type: 'string',
    enum: ['info', 'warning', 'error', 'interrupt'],
    title: 'NotificationType'
} as const;

export const OnboardingStatusSchema = {
    properties: {
        is_completed: {
            type: 'boolean',
            title: 'Is Completed'
        },
        current_step: {
            type: 'integer',
            title: 'Current Step'
        }
    },
    type: 'object',
    required: ['is_completed', 'current_step'],
    title: 'OnboardingStatus'
} as const;

export const PaginationMetaSchema = {
    properties: {
        page: {
            type: 'integer',
            title: 'Page',
            description: 'Current page number (1-based)'
        },
        take: {
            type: 'integer',
            title: 'Take',
            description: 'Number of items per page'
        },
        total_items: {
            type: 'integer',
            title: 'Total Items',
            description: 'Total number of items available'
        },
        total_pages: {
            type: 'integer',
            title: 'Total Pages',
            description: 'Total number of pages'
        },
        has_previous: {
            type: 'boolean',
            title: 'Has Previous',
            description: 'Whether there is a previous page'
        },
        has_next: {
            type: 'boolean',
            title: 'Has Next',
            description: 'Whether there is a next page'
        },
        start_index: {
            type: 'integer',
            title: 'Start Index',
            description: 'Starting index of current page items'
        },
        end_index: {
            type: 'integer',
            title: 'End Index',
            description: 'Ending index of current page items'
        }
    },
    type: 'object',
    required: ['page', 'take', 'total_items', 'total_pages', 'has_previous', 'has_next', 'start_index', 'end_index'],
    title: 'PaginationMeta'
} as const;

export const PaymentMethodResponseSchema = {
    properties: {
        id: {
            type: 'string',
            title: 'Id'
        },
        billing_details: {
            '$ref': '#/components/schemas/BillingDetails'
        },
        card: {
            '$ref': '#/components/schemas/CardDetails'
        },
        created: {
            type: 'integer',
            title: 'Created'
        },
        customer: {
            type: 'string',
            title: 'Customer'
        },
        livemode: {
            type: 'boolean',
            title: 'Livemode'
        },
        type: {
            type: 'string',
            title: 'Type'
        }
    },
    type: 'object',
    required: ['id', 'billing_details', 'card', 'created', 'customer', 'livemode', 'type'],
    title: 'PaymentMethodResponse'
} as const;

export const PlanChangeRequestCreateSchema = {
    properties: {
        first_name: {
            type: 'string',
            title: 'First Name'
        },
        last_name: {
            type: 'string',
            title: 'Last Name'
        },
        work_email: {
            type: 'string',
            title: 'Work Email'
        },
        work_title: {
            type: 'string',
            title: 'Work Title'
        },
        company_name: {
            type: 'string',
            title: 'Company Name'
        },
        reason: {
            type: 'string',
            title: 'Reason'
        },
        current_product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Current Product Id'
        },
        requested_price_id: {
            type: 'string',
            format: 'uuid',
            title: 'Requested Price Id'
        }
    },
    type: 'object',
    required: ['first_name', 'last_name', 'work_email', 'work_title', 'company_name', 'reason', 'current_product_id', 'requested_price_id'],
    title: 'PlanChangeRequestCreate'
} as const;

export const PlanChangeRequestResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        first_name: {
            type: 'string',
            title: 'First Name'
        },
        last_name: {
            type: 'string',
            title: 'Last Name'
        },
        work_email: {
            type: 'string',
            title: 'Work Email'
        },
        work_title: {
            type: 'string',
            title: 'Work Title'
        },
        company_name: {
            type: 'string',
            title: 'Company Name'
        },
        reason: {
            type: 'string',
            title: 'Reason'
        },
        status: {
            type: 'string',
            title: 'Status'
        },
        customer_id: {
            type: 'string',
            format: 'uuid',
            title: 'Customer Id'
        },
        current_product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Current Product Id'
        },
        requested_product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Requested Product Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'first_name', 'last_name', 'work_email', 'work_title', 'company_name', 'reason', 'status', 'customer_id', 'current_product_id', 'requested_product_id', 'created_at', 'updated_at'],
    title: 'PlanChangeRequestResponse'
} as const;

export const PresignedUrlRequestSchema = {
    properties: {
        kb_id: {
            type: 'string',
            title: 'Kb Id',
            description: 'ID of the knowledge base to upload files to'
        },
        files: {
            items: {
                '$ref': '#/components/schemas/FileInfo'
            },
            type: 'array',
            title: 'Files',
            description: 'Information about files to generate presigned URLs for'
        }
    },
    type: 'object',
    required: ['kb_id', 'files'],
    title: 'PresignedUrlRequest',
    description: 'Request to generate presigned URLs for file uploads'
} as const;

export const PresignedUrlResponseSchema = {
    properties: {
        kb_id: {
            type: 'string',
            title: 'Kb Id',
            description: 'Knowledge base ID'
        },
        presigned_urls: {
            items: {
                '$ref': '#/components/schemas/app__schemas__kb__PresignedUrlInfo'
            },
            type: 'array',
            title: 'Presigned Urls',
            description: 'Generated presigned URLs'
        }
    },
    type: 'object',
    required: ['kb_id', 'presigned_urls'],
    title: 'PresignedUrlResponse',
    description: 'Response with presigned URLs for file uploads'
} as const;

export const PriceResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        stripe_price_id: {
            type: 'string',
            title: 'Stripe Price Id'
        },
        product_id: {
            type: 'string',
            format: 'uuid',
            title: 'Product Id'
        },
        active: {
            type: 'boolean',
            title: 'Active'
        },
        amount: {
            type: 'number',
            title: 'Amount'
        },
        currency: {
            type: 'string',
            title: 'Currency'
        },
        interval: {
            type: 'string',
            title: 'Interval'
        }
    },
    type: 'object',
    required: ['id', 'stripe_price_id', 'product_id', 'active', 'amount', 'currency', 'interval'],
    title: 'PriceResponse'
} as const;

export const ProductResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        },
        description: {
            type: 'string',
            title: 'Description'
        },
        stripe_product_id: {
            type: 'string',
            title: 'Stripe Product Id'
        },
        active: {
            type: 'boolean',
            title: 'Active'
        },
        prices: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/PriceResponse'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Prices',
            default: []
        },
        quota_definition: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/QuotaDefinitionResponse'
                },
                {
                    type: 'null'
                }
            ]
        },
        is_custom: {
            type: 'boolean',
            title: 'Is Custom'
        }
    },
    type: 'object',
    required: ['id', 'name', 'description', 'stripe_product_id', 'active', 'is_custom'],
    title: 'ProductResponse'
} as const;

export const QuotaDefinitionResponseSchema = {
    properties: {
        max_workspaces: {
            type: 'integer',
            title: 'Max Workspaces'
        },
        max_members_per_workspace: {
            type: 'integer',
            title: 'Max Members Per Workspace'
        },
        max_fast_requests_per_month: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Max Fast Requests Per Month'
        }
    },
    type: 'object',
    required: ['max_workspaces', 'max_members_per_workspace', 'max_fast_requests_per_month'],
    title: 'QuotaDefinitionResponse'
} as const;

export const QuotaInfoSchema = {
    properties: {
        quota_used: {
            type: 'integer',
            title: 'Quota Used'
        },
        quota_limit: {
            type: 'integer',
            title: 'Quota Limit'
        },
        quota_remaining: {
            type: 'integer',
            title: 'Quota Remaining'
        },
        usage_percentage: {
            type: 'number',
            title: 'Usage Percentage'
        }
    },
    type: 'object',
    required: ['quota_used', 'quota_limit', 'quota_remaining', 'usage_percentage'],
    title: 'QuotaInfo'
} as const;

export const RecommendationCreateSchema = {
    properties: {
        type: {
            type: 'string',
            title: 'Type'
        },
        title: {
            type: 'string',
            maxLength: 255,
            title: 'Title'
        },
        description: {
            type: 'string',
            title: 'Description'
        },
        potential_savings: {
            type: 'number',
            title: 'Potential Savings'
        },
        effort: {
            type: 'string',
            maxLength: 50,
            title: 'Effort'
        },
        risk: {
            type: 'string',
            maxLength: 50,
            title: 'Risk'
        },
        status: {
            '$ref': '#/components/schemas/RecommendationStatus',
            default: 'pending'
        },
        resource_id: {
            type: 'string',
            format: 'uuid',
            title: 'Resource Id'
        }
    },
    type: 'object',
    required: ['type', 'title', 'description', 'potential_savings', 'effort', 'risk', 'resource_id'],
    title: 'RecommendationCreate'
} as const;

export const RecommendationOveralPublicSchema = {
    properties: {
        total_resource_scanned: {
            type: 'integer',
            title: 'Total Resource Scanned'
        },
        total_well_optimized: {
            type: 'integer',
            title: 'Total Well Optimized'
        },
        total_optimization_opportunities: {
            type: 'integer',
            title: 'Total Optimization Opportunities'
        },
        total_estimated_saving_amount: {
            type: 'number',
            title: 'Total Estimated Saving Amount'
        }
    },
    type: 'object',
    required: ['total_resource_scanned', 'total_well_optimized', 'total_optimization_opportunities', 'total_estimated_saving_amount'],
    title: 'RecommendationOveralPublic'
} as const;

export const RecommendationPublicSchema = {
    properties: {
        type: {
            type: 'string',
            title: 'Type'
        },
        title: {
            type: 'string',
            maxLength: 255,
            title: 'Title'
        },
        description: {
            type: 'string',
            title: 'Description'
        },
        potential_savings: {
            type: 'number',
            title: 'Potential Savings'
        },
        effort: {
            type: 'string',
            maxLength: 50,
            title: 'Effort'
        },
        risk: {
            type: 'string',
            maxLength: 50,
            title: 'Risk'
        },
        status: {
            '$ref': '#/components/schemas/RecommendationStatus',
            default: 'pending'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        resource_id: {
            type: 'string',
            format: 'uuid',
            title: 'Resource Id'
        },
        resource_name: {
            type: 'string',
            title: 'Resource Name'
        },
        resource_type: {
            type: 'string',
            title: 'Resource Type'
        },
        resource_provider: {
            '$ref': '#/components/schemas/CloudProvider'
        },
        resource_category: {
            '$ref': '#/components/schemas/ResourceCategory'
        }
    },
    type: 'object',
    required: ['type', 'title', 'description', 'potential_savings', 'effort', 'risk', 'id', 'resource_id', 'resource_name', 'resource_type', 'resource_provider', 'resource_category'],
    title: 'RecommendationPublic'
} as const;

export const RecommendationStatusSchema = {
    type: 'string',
    enum: ['pending', 'implemented', 'ignored', 'in_progress'],
    title: 'RecommendationStatus'
} as const;

export const RecommendationUpdateSchema = {
    properties: {
        type: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Type'
        },
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        potential_savings: {
            anyOf: [
                {
                    type: 'number'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Potential Savings'
        },
        effort: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 50
                },
                {
                    type: 'null'
                }
            ],
            title: 'Effort'
        },
        risk: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 50
                },
                {
                    type: 'null'
                }
            ],
            title: 'Risk'
        },
        status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/RecommendationStatus'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    title: 'RecommendationUpdate'
} as const;

export const RecommendationsPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/RecommendationPublic'
            },
            type: 'array',
            title: 'Data'
        },
        meta: {
            '$ref': '#/components/schemas/PaginationMeta'
        }
    },
    type: 'object',
    required: ['data', 'meta'],
    title: 'RecommendationsPublic'
} as const;

export const ReportSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        title: {
            type: 'string',
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        sections: {
            type: 'object',
            title: 'Sections',
            default: {}
        },
        executive_summary: {
            type: 'object',
            title: 'Executive Summary',
            default: {}
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['conversation_id', 'workspace_id', 'title'],
    title: 'Report'
} as const;

export const ResendActivationRequestSchema = {
    properties: {
        email: {
            type: 'string',
            format: 'email',
            title: 'Email'
        },
        captcha_token: {
            type: 'string',
            title: 'Captcha Token'
        }
    },
    type: 'object',
    required: ['email', 'captcha_token'],
    title: 'ResendActivationRequest'
} as const;

export const ResourceCategorySchema = {
    type: 'string',
    enum: ['COMPUTE', 'DATABASE', 'STORAGE', 'NETWORKING', 'SERVERLESS', 'CONTAINER', 'MESSAGING', 'MONITORING', 'SECURITY', 'MANAGEMENT', 'ANALYTICS', 'AI_ML', 'OTHER'],
    title: 'ResourceCategory'
} as const;

export const ResourcePublicSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        resource_id: {
            type: 'string',
            title: 'Resource Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        },
        region: {
            type: 'string',
            title: 'Region'
        },
        provider: {
            '$ref': '#/components/schemas/CloudProvider'
        },
        category: {
            '$ref': '#/components/schemas/ResourceCategory'
        },
        type: {
            type: 'string',
            title: 'Type'
        },
        status: {
            '$ref': '#/components/schemas/ResourceStatus'
        },
        total_recommendation: {
            type: 'integer',
            title: 'Total Recommendation'
        },
        total_potential_saving: {
            type: 'number',
            title: 'Total Potential Saving'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'resource_id', 'name', 'region', 'provider', 'category', 'type', 'status', 'total_recommendation', 'total_potential_saving', 'updated_at'],
    title: 'ResourcePublic'
} as const;

export const ResourcePublicSimpleSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        name: {
            type: 'string',
            title: 'Name'
        }
    },
    type: 'object',
    required: ['id', 'name'],
    title: 'ResourcePublicSimple'
} as const;

export const ResourceStatusSchema = {
    type: 'string',
    enum: ['stopped', 'starting', 'running', 'found', 'deleted'],
    title: 'ResourceStatus'
} as const;

export const ResourceTypeInfoSchema = {
    properties: {
        resource_type: {
            type: 'string',
            title: 'Resource Type'
        },
        category: {
            '$ref': '#/components/schemas/ResourceCategory'
        },
        display_name: {
            type: 'string',
            title: 'Display Name'
        },
        description: {
            type: 'string',
            title: 'Description'
        }
    },
    type: 'object',
    required: ['resource_type', 'category', 'display_name', 'description'],
    title: 'ResourceTypeInfo'
} as const;

export const ResourceTypesResponseSchema = {
    properties: {
        cloud_provider: {
            '$ref': '#/components/schemas/CloudProvider'
        },
        resource_types: {
            items: {
                '$ref': '#/components/schemas/ResourceTypeInfo'
            },
            type: 'array',
            title: 'Resource Types'
        }
    },
    type: 'object',
    required: ['cloud_provider', 'resource_types'],
    title: 'ResourceTypesResponse'
} as const;

export const ResourcesPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/ResourcePublic'
            },
            type: 'array',
            title: 'Data'
        },
        meta: {
            '$ref': '#/components/schemas/PaginationMeta'
        }
    },
    type: 'object',
    required: ['data', 'meta'],
    title: 'ResourcesPublic'
} as const;

export const RunModeEnumSchema = {
    type: 'string',
    enum: ['autonomous', 'agent'],
    title: 'RunModeEnum'
} as const;

export const ShareResponseSchema = {
    properties: {
        share_id: {
            type: 'string',
            format: 'uuid',
            title: 'Share Id'
        },
        is_shared: {
            type: 'boolean',
            title: 'Is Shared'
        },
        shared_at: {
            type: 'string',
            format: 'date-time',
            title: 'Shared At'
        },
        shared_by: {
            type: 'string',
            format: 'uuid',
            title: 'Shared By'
        }
    },
    type: 'object',
    required: ['share_id', 'is_shared', 'shared_at', 'shared_by'],
    title: 'ShareResponse'
} as const;

export const SimpleMessageSchema = {
    properties: {
        message: {
            type: 'string',
            title: 'Message'
        }
    },
    type: 'object',
    required: ['message'],
    title: 'SimpleMessage'
} as const;

export const StreamResponseSchema = {
    properties: {
        type: {
            type: 'string',
            title: 'Type'
        },
        content: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Content'
        },
        message_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Message Id'
        }
    },
    type: 'object',
    required: ['type'],
    title: 'StreamResponse'
} as const;

export const SubscriptionStatusSchema = {
    properties: {
        id: {
            type: 'string',
            title: 'Id'
        },
        customer_id: {
            type: 'string',
            title: 'Customer Id'
        },
        status: {
            type: 'string',
            title: 'Status'
        },
        current_period_end: {
            type: 'string',
            format: 'date-time',
            title: 'Current Period End'
        },
        cancel_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Cancel At'
        },
        product_name: {
            type: 'string',
            title: 'Product Name'
        },
        product_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Product Id'
        },
        price_amount: {
            type: 'number',
            title: 'Price Amount'
        },
        price_currency: {
            type: 'string',
            title: 'Price Currency'
        },
        price_interval: {
            type: 'string',
            title: 'Price Interval'
        }
    },
    type: 'object',
    required: ['id', 'customer_id', 'status', 'current_period_end', 'product_name', 'price_amount', 'price_currency', 'price_interval'],
    title: 'SubscriptionStatus'
} as const;

export const TaskCategoryEnumSchema = {
    type: 'string',
    enum: ['COST_OPTIMIZE', 'OPERATIONAL', 'SCALABILITY', 'SECURITY', 'OPERATIONAL_EFFICIENCY', 'OTHER'],
    title: 'TaskCategoryEnum',
    description: 'Enumeration of possible task categories.'
} as const;

export const TaskComplexitySchema = {
    type: 'string',
    enum: ['Easy', 'Medium'],
    title: 'TaskComplexity'
} as const;

export const TaskCouldEnumSchema = {
    type: 'string',
    enum: ['AWS', 'AZURE', 'GCP', 'ALL'],
    title: 'TaskCouldEnum'
} as const;

export const TaskCreateSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            default: ''
        },
        priority: {
            '$ref': '#/components/schemas/TaskPriority',
            default: 0
        },
        tags: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tags'
        },
        schedule: {
            type: 'string',
            title: 'Schedule'
        },
        agent_config: {
            '$ref': '#/components/schemas/AgentExecutionConfig'
        }
    },
    type: 'object',
    required: ['title', 'schedule', 'agent_config'],
    title: 'TaskCreate',
    description: 'Schema for creating a new task.'
} as const;

export const TaskDeleteResponseSchema = {
    properties: {
        status: {
            type: 'string',
            title: 'Status',
            default: 'success'
        }
    },
    type: 'object',
    title: 'TaskDeleteResponse',
    description: 'Schema for task delete response.'
} as const;

export const TaskEnableRequestSchema = {
    properties: {
        enable: {
            type: 'boolean',
            title: 'Enable',
            description: 'Whether to enable or disable the task'
        }
    },
    type: 'object',
    required: ['enable'],
    title: 'TaskEnableRequest',
    description: 'Schema for task enable request.'
} as const;

export const TaskExecutionStatusSchema = {
    type: 'string',
    enum: ['running', 'succeeded', 'failed', 'cancelled', 'required_approval'],
    title: 'TaskExecutionStatus',
    description: `Enumeration of execution statuses for task.

Attributes:
    RUNNING: Currently executing
    SUCCEEDED: Successfully completed
    FAILED: Execution failed
    CANCELLED: Execution cancelled
    REQUIRED_APPROVAL: Execution requires approval`
} as const;

export const TaskHistoriesResponseSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/TaskHistoryResponse'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['data', 'total'],
    title: 'TaskHistoriesResponse',
    description: 'Schema for task histories response.'
} as const;

export const TaskHistoryResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        task_id: {
            type: 'string',
            format: 'uuid',
            title: 'Task Id'
        },
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id'
        },
        status: {
            '$ref': '#/components/schemas/TaskExecutionStatus'
        },
        message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Message'
        },
        celery_task_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Celery Task Id'
        },
        start_time: {
            type: 'string',
            format: 'date-time',
            title: 'Start Time'
        },
        end_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'End Time'
        },
        run_time: {
            anyOf: [
                {
                    type: 'number'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Run Time'
        }
    },
    type: 'object',
    required: ['id', 'task_id', 'conversation_id', 'status', 'start_time'],
    title: 'TaskHistoryResponse',
    description: 'Schema for task history.'
} as const;

export const TaskListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/TaskResponse'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total',
            default: 0
        }
    },
    type: 'object',
    title: 'TaskList',
    description: 'Schema for paginated task list.'
} as const;

export const TaskPrioritySchema = {
    type: 'integer',
    enum: [0, 1, 2, 3],
    title: 'TaskPriority',
    description: `Enumeration of task priority levels.

Attributes:
    LOW (0): Regular priority, no urgency
    MEDIUM (1): Moderate priority, should be done soon
    HIGH (2): High priority, urgent attention needed
    CRITICAL (3): Critical priority, requires immediate attention`
} as const;

export const TaskResponseSchema = {
    properties: {
        title: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            default: ''
        },
        priority: {
            '$ref': '#/components/schemas/TaskPriority',
            default: 0
        },
        tags: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Tags'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        owner_id: {
            type: 'string',
            format: 'uuid',
            title: 'Owner Id'
        },
        scheduled_status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskScheduledStatus'
                },
                {
                    type: 'null'
                }
            ]
        },
        execution_status: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskExecutionStatus'
                },
                {
                    type: 'null'
                }
            ]
        },
        error: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Error'
        },
        last_run: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Run'
        },
        next_run: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Next Run'
        },
        schedule: {
            type: 'string',
            title: 'Schedule'
        },
        agent_config: {
            '$ref': '#/components/schemas/AgentExecutionConfig'
        },
        enable: {
            type: 'boolean',
            title: 'Enable',
            default: true
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        created_by: {
            type: 'string',
            format: 'uuid',
            title: 'Created By'
        },
        updated_by: {
            type: 'string',
            format: 'uuid',
            title: 'Updated By'
        }
    },
    type: 'object',
    required: ['title', 'id', 'workspace_id', 'owner_id', 'schedule', 'agent_config', 'created_at', 'updated_at', 'created_by', 'updated_by'],
    title: 'TaskResponse',
    description: 'Schema for task response.'
} as const;

export const TaskScheduledStatusSchema = {
    type: 'string',
    enum: ['pending', 'scheduled'],
    title: 'TaskScheduledStatus',
    description: 'Enumeration of scheduled statuses for task.'
} as const;

export const TaskServiceEnumSchema = {
    type: 'string',
    enum: ['ALL', 'OTHER', 'COMPUTE', 'STORAGE', 'SERVERLESS', 'DATABASE', 'NETWORK', 'MESSAGING', 'MANAGEMENT', 'BILLING', 'CROSS_SERVICE', 'MONITORING', 'STREAMING', 'SECURITY'],
    title: 'TaskServiceEnum',
    description: 'Enumeration of possible task services.'
} as const;

export const TaskStopRequestSchema = {
    properties: {
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id',
            description: 'Conversation ID to stop'
        }
    },
    type: 'object',
    required: ['conversation_id'],
    title: 'TaskStopRequest',
    description: 'Schema for task stop request.'
} as const;

export const TaskStopResponseSchema = {
    properties: {
        task_id: {
            type: 'string',
            format: 'uuid',
            title: 'Task Id'
        },
        conversation_id: {
            type: 'string',
            format: 'uuid',
            title: 'Conversation Id'
        },
        status: {
            type: 'string',
            title: 'Status'
        }
    },
    type: 'object',
    required: ['task_id', 'conversation_id', 'status'],
    title: 'TaskStopResponse',
    description: 'Schema for task stop response.'
} as const;

export const TaskTemplateSchema = {
    properties: {
        title: {
            type: 'string',
            title: 'Title'
        },
        description: {
            type: 'string',
            title: 'Description'
        },
        icon: {
            type: 'string',
            title: 'Icon'
        },
        impact: {
            type: 'string',
            title: 'Impact'
        },
        complexity: {
            '$ref': '#/components/schemas/TaskComplexity'
        },
        duration: {
            type: 'string',
            title: 'Duration'
        }
    },
    type: 'object',
    required: ['title', 'description', 'icon', 'impact', 'complexity', 'duration'],
    title: 'TaskTemplate'
} as const;

export const TaskTemplateCreateSchema = {
    properties: {
        task: {
            type: 'string',
            title: 'Task'
        },
        category: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskCategoryEnum'
                },
                {
                    type: 'null'
                }
            ],
            default: 'OTHER'
        },
        service: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskServiceEnum'
                },
                {
                    type: 'null'
                }
            ],
            default: 'OTHER'
        },
        service_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Service Name',
            default: ''
        },
        cloud: {
            '$ref': '#/components/schemas/TaskCouldEnum'
        },
        run_mode: {
            '$ref': '#/components/schemas/RunModeEnum'
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        context: {
            type: 'string',
            title: 'Context'
        }
    },
    type: 'object',
    required: ['task', 'cloud', 'run_mode', 'context'],
    title: 'TaskTemplateCreate'
} as const;

export const TaskTemplateListSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/app__schemas__task_template__TaskTemplateResponse'
            },
            type: 'array',
            title: 'Data'
        },
        total: {
            type: 'integer',
            title: 'Total'
        }
    },
    type: 'object',
    required: ['data', 'total'],
    title: 'TaskTemplateList'
} as const;

export const TaskTemplateSelectionSchema = {
    properties: {
        selected_template_title: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Selected Template Title'
        },
        action: {
            type: 'string',
            title: 'Action'
        }
    },
    type: 'object',
    required: ['action'],
    title: 'TaskTemplateSelection'
} as const;

export const TaskTemplateUpdateSchema = {
    properties: {
        task: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Task'
        },
        category: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskCategoryEnum'
                },
                {
                    type: 'null'
                }
            ]
        },
        service: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/TaskServiceEnum'
                },
                {
                    type: 'null'
                }
            ]
        },
        run_mode: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/RunModeEnum'
                },
                {
                    type: 'null'
                }
            ]
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        context: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Context'
        }
    },
    type: 'object',
    title: 'TaskTemplateUpdate'
} as const;

export const TaskUpdateSchema = {
    properties: {
        title: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    minLength: 1
                },
                {
                    type: 'null'
                }
            ],
            title: 'Title'
        },
        description: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description',
            default: ''
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        }
    },
    type: 'object',
    title: 'TaskUpdate',
    description: 'Schema for updating an existing task.'
} as const;

export const TokenSchema = {
    properties: {
        access_token: {
            type: 'string',
            title: 'Access Token'
        },
        token_type: {
            type: 'string',
            title: 'Token Type',
            default: 'bearer'
        },
        workspace_id: {
            anyOf: [
                {
                    type: 'string',
                    format: 'uuid'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Workspace Id'
        },
        is_first_login: {
            type: 'boolean',
            title: 'Is First Login',
            default: false
        },
        slack_oauth: {
            type: 'boolean',
            title: 'Slack Oauth',
            default: false
        },
        app_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'App Id'
        },
        team_id: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Team Id'
        }
    },
    type: 'object',
    required: ['access_token'],
    title: 'Token'
} as const;

export const TokenDistributionCategorySchema = {
    properties: {
        category: {
            type: 'string',
            title: 'Category'
        },
        percentage: {
            type: 'number',
            title: 'Percentage'
        }
    },
    type: 'object',
    required: ['category', 'percentage'],
    title: 'TokenDistributionCategory'
} as const;

export const TokenUsageCreateSchema = {
    properties: {
        message_id: {
            type: 'string',
            format: 'uuid',
            title: 'Message ID',
            description: 'Unique identifier of the associated message'
        },
        input_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Input Tokens',
            description: 'Number of tokens in the input text',
            example: 100
        },
        output_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Output Tokens',
            description: 'Number of tokens in the output text',
            example: 150
        },
        model_id: {
            type: 'string',
            title: 'Model ID',
            description: 'Identifier of the AI model used',
            example: 'gpt-4'
        }
    },
    type: 'object',
    required: ['message_id', 'input_tokens', 'output_tokens', 'model_id'],
    title: 'TokenUsageCreate'
} as const;

export const TokenUsageResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'ID',
            description: 'Unique identifier for the usage record'
        },
        message_id: {
            type: 'string',
            format: 'uuid',
            title: 'Message ID',
            description: 'ID of the associated message'
        },
        input_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Input Tokens',
            description: 'Number of tokens in the input text'
        },
        output_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Output Tokens',
            description: 'Number of tokens in the output text'
        },
        model_id: {
            type: 'string',
            title: 'Model ID',
            description: 'Identifier of the AI model used'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace ID',
            description: 'ID of the workspace'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At',
            description: 'Timestamp of record creation'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At',
            description: 'Timestamp of last update'
        },
        total_tokens: {
            type: 'integer',
            title: 'Total Tokens',
            description: 'Calculate total tokens from input and output tokens.',
            readOnly: true
        }
    },
    type: 'object',
    required: ['id', 'message_id', 'input_tokens', 'output_tokens', 'model_id', 'workspace_id', 'created_at', 'total_tokens'],
    title: 'TokenUsageResponse',
    description: `Schema for token usage response.

Attributes:
    id: Unique identifier for the usage record
    message_id: ID of the associated message
    input_tokens: Number of tokens in input text
    output_tokens: Number of tokens in output text
    model_id: ID of the AI model used
    total_tokens: Total number of tokens used
    created_at: Timestamp of record creation`
} as const;

export const URLsUploadRequestSchema = {
    properties: {
        urls: {
            anyOf: [
                {
                    items: {
                        type: 'string'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Urls',
            description: 'URLs to crawl (required if source_type is website)'
        },
        deep_crawls: {
            anyOf: [
                {
                    items: {
                        type: 'boolean'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Deep Crawls',
            description: 'Whether to enable deep crawling for each URL'
        }
    },
    type: 'object',
    title: 'URLsUploadRequest'
} as const;

export const UpdatePasswordSchema = {
    properties: {
        current_password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'Current Password'
        },
        new_password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'New Password'
        }
    },
    type: 'object',
    required: ['current_password', 'new_password'],
    title: 'UpdatePassword'
} as const;

export const UploadedAttachmentInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side unique ID for the file.'
        },
        filename: {
            type: 'string',
            title: 'Filename',
            description: 'Sanitized name of the file.'
        },
        storage_key: {
            type: 'string',
            title: 'Storage Key',
            description: 'The key for the object in storage.'
        },
        content_type: {
            type: 'string',
            title: 'Content Type',
            description: 'MIME type of the file.'
        },
        file_size: {
            type: 'integer',
            title: 'File Size',
            description: 'Size of the file in bytes.'
        }
    },
    type: 'object',
    required: ['file_id', 'filename', 'storage_key', 'content_type', 'file_size'],
    title: 'UploadedAttachmentInfo'
} as const;

export const UploadedFileInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side ID for tracking this file'
        },
        filename: {
            type: 'string',
            title: 'Filename',
            description: 'Original filename'
        },
        storage_key: {
            type: 'string',
            title: 'Storage Key',
            description: 'Storage key for the uploaded file'
        },
        content_type: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Content Type',
            description: 'File MIME type'
        },
        file_size: {
            anyOf: [
                {
                    type: 'integer'
                },
                {
                    type: 'null'
                }
            ],
            title: 'File Size',
            description: 'File size in bytes'
        }
    },
    type: 'object',
    required: ['file_id', 'filename', 'storage_key'],
    title: 'UploadedFileInfo',
    description: 'Information about a successfully uploaded file'
} as const;

export const UsageQuotaResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'ID'
        },
        user_id: {
            type: 'string',
            format: 'uuid',
            title: 'User ID'
        },
        quota_used_messages: {
            type: 'integer',
            title: 'Quota Used Messages'
        },
        quota_used_tokens: {
            type: 'integer',
            title: 'Quota Used Tokens'
        },
        reset_at: {
            type: 'string',
            format: 'date-time',
            title: 'Reset At'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'user_id', 'quota_used_messages', 'quota_used_tokens', 'reset_at', 'created_at'],
    title: 'UsageQuotaResponse',
    description: 'Response schema for usage quota information.'
} as const;

export const UsageStatisticsSchema = {
    properties: {
        input_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Input Tokens'
        },
        output_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Output Tokens'
        },
        total_tokens: {
            type: 'integer',
            minimum: 0,
            title: 'Total Tokens'
        },
        quota_limit: {
            type: 'integer',
            minimum: 0,
            title: 'Quota Limit'
        },
        quota_used: {
            type: 'integer',
            minimum: 0,
            title: 'Quota Used'
        },
        quota_remaining: {
            type: 'integer',
            minimum: 0,
            title: 'Quota Remaining'
        },
        usage_percentage: {
            type: 'number',
            minimum: 0,
            title: 'Usage Percentage'
        },
        daily_token_usage: {
            items: {
                '$ref': '#/components/schemas/DailyTokenUsage'
            },
            type: 'array',
            title: 'Daily Token Usage'
        },
        agent_type_stats: {
            items: {
                '$ref': '#/components/schemas/AgentTypeUsage'
            },
            type: 'array',
            title: 'Agent Type Stats'
        }
    },
    type: 'object',
    required: ['input_tokens', 'output_tokens', 'total_tokens', 'quota_limit', 'quota_used', 'quota_remaining', 'usage_percentage', 'daily_token_usage', 'agent_type_stats'],
    title: 'UsageStatistics',
    description: 'Response schema for usage statistics.'
} as const;

export const UserCreateSchema = {
    properties: {
        email: {
            type: 'string',
            maxLength: 255,
            format: 'email',
            title: 'Email'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: false
        },
        is_email_verified: {
            type: 'boolean',
            title: 'Is Email Verified',
            default: false
        },
        is_superuser: {
            type: 'boolean',
            title: 'Is Superuser',
            default: false
        },
        last_login_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Login Time'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        },
        password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'Password'
        }
    },
    type: 'object',
    required: ['email', 'password'],
    title: 'UserCreate'
} as const;

export const UserDetailSchema = {
    properties: {
        email: {
            type: 'string',
            maxLength: 255,
            format: 'email',
            title: 'Email'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: false
        },
        is_email_verified: {
            type: 'boolean',
            title: 'Is Email Verified',
            default: false
        },
        is_superuser: {
            type: 'boolean',
            title: 'Is Superuser',
            default: false
        },
        last_login_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Login Time'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspaces: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/Workspace'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Workspaces'
        },
        own_workspaces: {
            anyOf: [
                {
                    items: {
                        '$ref': '#/components/schemas/Workspace'
                    },
                    type: 'array'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Own Workspaces'
        }
    },
    type: 'object',
    required: ['email', 'id'],
    title: 'UserDetail'
} as const;

export const UserPublicSchema = {
    properties: {
        email: {
            type: 'string',
            maxLength: 255,
            format: 'email',
            title: 'Email'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: false
        },
        is_email_verified: {
            type: 'boolean',
            title: 'Is Email Verified',
            default: false
        },
        is_superuser: {
            type: 'boolean',
            title: 'Is Superuser',
            default: false
        },
        last_login_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Login Time'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        }
    },
    type: 'object',
    required: ['email', 'id'],
    title: 'UserPublic'
} as const;

export const UserRegisterSchema = {
    properties: {
        email: {
            type: 'string',
            maxLength: 255,
            format: 'email',
            title: 'Email'
        },
        password: {
            type: 'string',
            maxLength: 40,
            minLength: 8,
            title: 'Password'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        }
    },
    type: 'object',
    required: ['email', 'password'],
    title: 'UserRegister'
} as const;

export const UserUpdateSchema = {
    properties: {
        email: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    format: 'email'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Email'
        },
        is_active: {
            type: 'boolean',
            title: 'Is Active',
            default: false
        },
        is_email_verified: {
            type: 'boolean',
            title: 'Is Email Verified',
            default: false
        },
        is_superuser: {
            type: 'boolean',
            title: 'Is Superuser',
            default: false
        },
        last_login_time: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Last Login Time'
        },
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        },
        password: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 40,
                    minLength: 8
                },
                {
                    type: 'null'
                }
            ],
            title: 'Password'
        }
    },
    type: 'object',
    title: 'UserUpdate'
} as const;

export const UserUpdateMeSchema = {
    properties: {
        full_name: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255
                },
                {
                    type: 'null'
                }
            ],
            title: 'Full Name'
        },
        email: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 255,
                    format: 'email'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Email'
        },
        avatar_url: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1024
                },
                {
                    type: 'null'
                }
            ],
            title: 'Avatar Url'
        }
    },
    type: 'object',
    title: 'UserUpdateMe'
} as const;

export const UsersPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/UserPublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'UsersPublic'
} as const;

export const ValidationErrorSchema = {
    properties: {
        loc: {
            items: {
                anyOf: [
                    {
                        type: 'string'
                    },
                    {
                        type: 'integer'
                    }
                ]
            },
            type: 'array',
            title: 'Location'
        },
        msg: {
            type: 'string',
            title: 'Message'
        },
        type: {
            type: 'string',
            title: 'Error Type'
        }
    },
    type: 'object',
    required: ['loc', 'msg', 'type'],
    title: 'ValidationError'
} as const;

export const WorkspaceSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        organization_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Organization Name'
        },
        provider: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/CloudProvider'
                },
                {
                    type: 'null'
                }
            ]
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        owner_id: {
            type: 'string',
            format: 'uuid',
            title: 'Owner Id'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        is_default: {
            type: 'boolean',
            title: 'Is Default',
            default: false
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted',
            default: false
        }
    },
    type: 'object',
    required: ['name', 'owner_id'],
    title: 'Workspace'
} as const;

export const WorkspaceBuiltInToolResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        required_permission: {
            type: 'boolean',
            title: 'Required Permission'
        },
        builtin_tool: {
            '$ref': '#/components/schemas/BuiltInToolPublic'
        }
    },
    type: 'object',
    required: ['id', 'required_permission', 'builtin_tool'],
    title: 'WorkspaceBuiltInToolResponse'
} as const;

export const WorkspaceCreateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        organization_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Organization Name'
        },
        provider: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/CloudProvider'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    required: ['name'],
    title: 'WorkspaceCreate'
} as const;

export const WorkspaceDetailSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        organization_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Organization Name'
        },
        provider: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/CloudProvider'
                },
                {
                    type: 'null'
                }
            ]
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        is_default: {
            type: 'boolean',
            title: 'Is Default'
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted',
            default: false
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        },
        aws_account: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/AWSAccountDetail'
                },
                {
                    type: 'null'
                }
            ]
        },
        gcp_account: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/GCPAccountDetail'
                },
                {
                    type: 'null'
                }
            ]
        },
        settings: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/WorkspaceSetting'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    required: ['name', 'id', 'is_default', 'settings'],
    title: 'WorkspaceDetail'
} as const;

export const WorkspacePublicSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        organization_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Organization Name'
        },
        provider: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/CloudProvider'
                },
                {
                    type: 'null'
                }
            ]
        },
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        is_default: {
            type: 'boolean',
            title: 'Is Default'
        },
        is_deleted: {
            type: 'boolean',
            title: 'Is Deleted',
            default: false
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            type: 'string',
            format: 'date-time',
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['name', 'id', 'is_default'],
    title: 'WorkspacePublic'
} as const;

export const WorkspaceSettingSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        workspace_id: {
            type: 'string',
            format: 'uuid',
            title: 'Workspace Id'
        },
        provider: {
            '$ref': '#/components/schemas/CloudProvider',
            default: 'AWS'
        },
        regions: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Regions',
            default: []
        },
        types: {
            items: {
                type: 'string'
            },
            type: 'array',
            title: 'Types',
            default: []
        },
        cron_pattern: {
            type: 'string',
            maxLength: 250,
            title: 'Cron Pattern'
        }
    },
    type: 'object',
    required: ['workspace_id', 'cron_pattern'],
    title: 'WorkspaceSetting',
    description: 'Settings for a workspace'
} as const;

export const WorkspaceUpdateSchema = {
    properties: {
        name: {
            type: 'string',
            maxLength: 255,
            minLength: 1,
            title: 'Name'
        },
        description: {
            anyOf: [
                {
                    type: 'string',
                    maxLength: 1000
                },
                {
                    type: 'null'
                }
            ],
            title: 'Description'
        },
        organization_name: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Organization Name'
        },
        provider: {
            anyOf: [
                {
                    '$ref': '#/components/schemas/CloudProvider'
                },
                {
                    type: 'null'
                }
            ]
        }
    },
    type: 'object',
    required: ['name'],
    title: 'WorkspaceUpdate'
} as const;

export const WorkspacesPublicSchema = {
    properties: {
        data: {
            items: {
                '$ref': '#/components/schemas/WorkspacePublic'
            },
            type: 'array',
            title: 'Data'
        },
        count: {
            type: 'integer',
            title: 'Count'
        }
    },
    type: 'object',
    required: ['data', 'count'],
    title: 'WorkspacesPublic'
} as const;

export const app__schemas__kb__PresignedUrlInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side ID for tracking this file'
        },
        filename: {
            type: 'string',
            title: 'Filename',
            description: 'Original filename'
        },
        storage_key: {
            type: 'string',
            title: 'Storage Key',
            description: 'Storage key for the file'
        },
        presigned_url: {
            type: 'string',
            title: 'Presigned Url',
            description: 'Presigned URL for file upload'
        }
    },
    type: 'object',
    required: ['file_id', 'filename', 'storage_key', 'presigned_url'],
    title: 'PresignedUrlInfo',
    description: 'Information about a generated presigned URL'
} as const;

export const app__schemas__kb__TaskStatusResponseSchema = {
    properties: {
        task_id: {
            type: 'string',
            title: 'Task Id',
            description: 'Celery task ID'
        },
        status: {
            '$ref': '#/components/schemas/AsyncTaskStatus',
            description: 'Task status (PENDING, PROGRESS, SUCCESS, FAILURE)'
        },
        progress: {
            type: 'integer',
            title: 'Progress',
            description: 'Progress percentage (0-100)',
            default: 0
        },
        result: {
            anyOf: [
                {
                    type: 'object'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Result',
            description: 'Task result if completed'
        },
        error: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Error',
            description: 'Error message if failed'
        },
        status_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Status Message',
            description: 'Human-readable status message'
        }
    },
    type: 'object',
    required: ['task_id', 'status'],
    title: 'TaskStatusResponse',
    description: 'Response schema for task status operations'
} as const;

export const app__schemas__message_attachment__PresignedUrlInfoSchema = {
    properties: {
        file_id: {
            type: 'string',
            title: 'File Id',
            description: 'Client-side unique ID for the file.'
        },
        filename: {
            type: 'string',
            title: 'Filename',
            description: 'Sanitized name of the file.'
        },
        storage_key: {
            type: 'string',
            title: 'Storage Key',
            description: 'The key for the object in storage.'
        },
        presigned_url: {
            type: 'string',
            title: 'Presigned Url',
            description: 'The presigned URL for uploading.'
        }
    },
    type: 'object',
    required: ['file_id', 'filename', 'storage_key', 'presigned_url'],
    title: 'PresignedUrlInfo'
} as const;

export const app__schemas__message_attachment__TaskStatusResponseSchema = {
    properties: {
        task_id: {
            type: 'string',
            title: 'Task Id'
        },
        status: {
            '$ref': '#/components/schemas/AsyncTaskStatus'
        },
        status_message: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Status Message'
        },
        progress: {
            type: 'integer',
            title: 'Progress',
            default: 0
        },
        result: {
            anyOf: [
                {
                    type: 'object'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Result'
        },
        error: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Error'
        }
    },
    type: 'object',
    required: ['task_id', 'status'],
    title: 'TaskStatusResponse'
} as const;

export const app__schemas__onboarding__TaskTemplateResponseSchema = {
    properties: {
        cloud_provider: {
            type: 'string',
            title: 'Cloud Provider'
        },
        templates: {
            items: {
                '$ref': '#/components/schemas/TaskTemplate'
            },
            type: 'array',
            title: 'Templates'
        }
    },
    type: 'object',
    required: ['cloud_provider', 'templates'],
    title: 'TaskTemplateResponse'
} as const;

export const app__schemas__task_template__TaskTemplateResponseSchema = {
    properties: {
        id: {
            type: 'string',
            format: 'uuid',
            title: 'Id'
        },
        task: {
            type: 'string',
            title: 'Task'
        },
        category: {
            '$ref': '#/components/schemas/TaskCategoryEnum'
        },
        service: {
            '$ref': '#/components/schemas/TaskServiceEnum'
        },
        service_name: {
            type: 'string',
            title: 'Service Name'
        },
        cloud: {
            '$ref': '#/components/schemas/TaskCouldEnum'
        },
        run_mode: {
            '$ref': '#/components/schemas/RunModeEnum'
        },
        schedule: {
            anyOf: [
                {
                    type: 'string'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Schedule'
        },
        context: {
            type: 'string',
            title: 'Context'
        },
        is_default: {
            type: 'boolean',
            title: 'Is Default'
        },
        created_at: {
            type: 'string',
            format: 'date-time',
            title: 'Created At'
        },
        updated_at: {
            anyOf: [
                {
                    type: 'string',
                    format: 'date-time'
                },
                {
                    type: 'null'
                }
            ],
            title: 'Updated At'
        }
    },
    type: 'object',
    required: ['id', 'task', 'category', 'service', 'service_name', 'cloud', 'run_mode', 'schedule', 'context', 'is_default', 'created_at', 'updated_at'],
    title: 'TaskTemplateResponse'
} as const;