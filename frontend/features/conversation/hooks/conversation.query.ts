import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useInfiniteQuery,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import { ConversationQueryParams } from '../models/conversation.type';
import { conversationApi } from '../services/conversation.api';
import { conversationAgentApi } from '../services/conversation-agent.api';
import { CONVERSATION_CONFIG, CONVERSATION_MESSAGES } from '../config/conversation.config';

const conversationQueryKeys = createQueryKeys(CacheKey.CONVERSATIONS, {
  list: (params: ConversationQueryParams) => ({
    queryKey: [params],
    queryFn: () => conversationApi.list(params),
  }),

  byAgent: (agentId: string, resourceId?: string) => ({
    queryKey: [{ agentId, resourceId }],
    queryFn: () => conversationApi.getWithAgent(agentId, resourceId),
  }),

  infinite: (params: ConversationQueryParams) => ({
    queryKey: [params],
  }),
});

// Query hooks
const useList = (params: ConversationQueryParams) => {
  return useQuery({
    ...conversationQueryKeys.list(params),
    staleTime: CONVERSATION_CONFIG.CACHE.STALE_TIME,
    gcTime: CONVERSATION_CONFIG.CACHE.GC_TIME,
    placeholderData: keepPreviousData,
  });
};

const useByAgent = (agentId: string, resourceId?: string) => {
  return useQuery({
    ...conversationQueryKeys.byAgent(agentId, resourceId),
    enabled: !!agentId,
    staleTime: CONVERSATION_CONFIG.CACHE.STALE_TIME,
    gcTime: CONVERSATION_CONFIG.CACHE.GC_TIME,
  });
};

const useInfiniteList = (params: ConversationQueryParams) => {
  return useInfiniteQuery({
    queryKey: conversationQueryKeys.infinite(params).queryKey,
    queryFn: ({ pageParam = 0 }) =>
      conversationApi.list({
        ...params,
        skip: pageParam * 20,
        limit: 20,
      }),
    initialPageParam: 0,
    getNextPageParam: (lastPage, _, lastPageParam) => {
      return lastPage.data.length === 20 ? lastPageParam + 1 : undefined;
    },
    placeholderData: keepPreviousData,
  });
};

// Mutation hooks
const useCreate = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: conversationApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.CONVERSATIONS] });
      toast.success(CONVERSATION_MESSAGES.SUCCESS.CREATED);
    },
    onError: () => {
      toast.error(CONVERSATION_MESSAGES.ERROR.CREATE_FAILED);
    },
  });
};

const useCreateWithAgent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ agentId, resourceId }: { agentId: string; resourceId?: string }) =>
      conversationApi.createWithAgent(agentId, resourceId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.CONVERSATIONS] });
      toast.success(CONVERSATION_MESSAGES.SUCCESS.CREATED);
    },
    onError: () => {
      toast.error(CONVERSATION_MESSAGES.ERROR.CREATE_FAILED);
    },
  });
};

const useCreateWithDefaultAgent = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: conversationAgentApi.createConversationWithDefaultAgent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.CONVERSATIONS] });
      toast.success(CONVERSATION_MESSAGES.SUCCESS.CREATED);
    },
    onError: (error: Error) => {
      const message = error.message === 'No autonomous agents available'
        ? CONVERSATION_MESSAGES.ERROR.NO_AGENTS
        : CONVERSATION_MESSAGES.ERROR.CREATE_FAILED;
      toast.error(message);
    },
  });
};

const useRename = (conversationId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: conversationApi.detail(conversationId).rename,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.CONVERSATIONS] });
      toast.success(CONVERSATION_MESSAGES.SUCCESS.RENAMED);
    },
    onError: () => {
      toast.error(CONVERSATION_MESSAGES.ERROR.RENAME_FAILED);
    },
  });
};

const useDelete = (conversationId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: conversationApi.detail(conversationId).delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.CONVERSATIONS] });
      toast.success(CONVERSATION_MESSAGES.SUCCESS.DELETED);
    },
    onError: () => {
      toast.error(CONVERSATION_MESSAGES.ERROR.DELETE_FAILED);
    },
  });
};

export const conversationQuery = {
  query: {
    useList,
    useByAgent,
    useInfiniteList,
  },
  mutation: {
    useCreate,
    useCreateWithAgent,
    useCreateWithDefaultAgent,
    useRename,
    useDelete,
  },
};
