import { useCallback } from 'react';
import { toast } from 'sonner';
import { conversationQuery } from './conversation.query';
import { useConversationNavigation } from './use-conversation-navigation';
import { CONVERSATION_MESSAGES } from '../config/conversation.config';

interface CreateConversationOptions {
  agentId?: string;
  resourceId?: string;
  initialMessage?: string;
  onSuccess?: (conversationId: string) => void;
  onError?: (error: Error) => void;
  skipNavigation?: boolean;
}

export const useConversationCreator = () => {
  const createWithAgent = conversationQuery.mutation.useCreateWithAgent();
  const createWithDefaultAgent = conversationQuery.mutation.useCreateWithDefaultAgent();
  const { navigateToConversation } = useConversationNavigation();

  const createAndNavigate = useCallback(
    async (options: CreateConversationOptions) => {
      try {
        // Show loading toast
        toast.loading(CONVERSATION_MESSAGES.LOADING.CREATING);

        let conversation;
        let agentId = options.agentId;

        if (agentId) {
          // Create with specific agent
          conversation = await createWithAgent.mutateAsync({
            agentId,
            resourceId: options.resourceId,
          });
        } else {
          // Create with default autonomous agent
          conversation = await createWithDefaultAgent.mutateAsync(options.resourceId);
          agentId = conversation.agent_id;
        }

        // Dismiss loading toast
        toast.dismiss();

        // Navigate to the conversation unless explicitly skipped
        if (!options.skipNavigation && agentId) {
          navigateToConversation(agentId, conversation.id, options.initialMessage);
        }

        // Call success callback
        options.onSuccess?.(conversation.id);

        return conversation;
      } catch (error) {
        // Dismiss loading toast
        toast.dismiss();

        console.error('Failed to create conversation:', error);
        const errorObj = error instanceof Error ? error : new Error(String(error));

        // Call error callback
        options.onError?.(errorObj);

        throw errorObj;
      }
    },
    [createWithAgent, createWithDefaultAgent, navigateToConversation]
  );

  const createOnly = useCallback(
    async (options: Omit<CreateConversationOptions, 'skipNavigation'>) => {
      return createAndNavigate({ ...options, skipNavigation: true });
    },
    [createAndNavigate]
  );

  return {
    /**
     * Create a conversation and navigate to it
     */
    createAndNavigate,

    /**
     * Create a conversation without navigation
     */
    createOnly,

    /**
     * Whether any conversation creation is in progress
     */
    isCreating: createWithAgent.isPending || createWithDefaultAgent.isPending,

    /**
     * Error from the last creation attempt
     */
    error: createWithAgent.error || createWithDefaultAgent.error,
  };
};
