'use client';

import { useRouter } from 'next/navigation';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { PageSkeleton } from '@/components/ui/common/page';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import pathsConfig from '@/config/paths.config';
import { getAutonomousAgentId } from '@/features/agent/services/agent.api';
import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2Icon, RocketIcon, SkipForwardIcon } from 'lucide-react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';

import { onboardingQuery } from '../hooks/onboarding.query';
import { OnboardingTaskTemplateCard } from './onboarding-task-template-card';

const schema = z.object({
  selected_template_title: z.string().min(1),
});

type Schema = z.infer<typeof schema>;

export function OnboardingStepThree() {
  const router = useRouter();
  const form = useForm<Schema>({
    resolver: zodResolver(schema),
  });
  const { data, isLoading } = onboardingQuery.query.useGetTaskTemplates();

  const { control, handleSubmit, watch } = form;
  const { mutate, isPending } =
    onboardingQuery.mutation.useCompleteTaskTemplate();

  const onSubmit: SubmitHandler<Schema> = (data) => {
    mutate(
      {
        selected_template_title: data.selected_template_title,
        action: 'submit',
      },
      {
        onSuccess: async () => {
          const agentId = await getAutonomousAgentId();
          router.replace(pathsConfig.app.agentDetail(agentId));
        },
      },
    );
  };

  const onSkip = () => {
    mutate({
      selected_template_title: null,
      action: 'skip',
    });
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>{`Experience CloudThinker's Agentic Operations`}</CardTitle>
            <CardDescription>
              Watch CostOps, FinOps, and Security agents work autonomously
            </CardDescription>
            {/* TODO: Add alert when sandbox is available */}
            {/* <Alert variant="info">
              <AlertTitle className="flex items-center gap-2">
                <SparklesIcon className="size-4" />
                <span>{`Demo using simulated enterprise cloud data - you'll experience the full AppStudio interface`}</span>
              </AlertTitle>
            </Alert> */}
          </CardHeader>
          <CardContent>
            <If condition={data}>
              {(data) => (
                <FormField
                  control={control}
                  name="selected_template_title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>CloudThinker Operations</FormLabel>
                      <FormDescription>
                        {`Select one or more agentic operations to experience in CloudThinker's AppStudio environment`}
                      </FormDescription>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          value={field.value}
                          className="grid grid-cols-1 gap-4 lg:grid-cols-3"
                        >
                          {data.templates.map((taskTemplate) => (
                            <FormItem
                              key={taskTemplate.title}
                              className="flex flex-row items-center space-y-0 space-x-2"
                            >
                              <FormLabel className="grow">
                                <FormControl className="sr-only">
                                  <RadioGroupItem value={taskTemplate.title} />
                                </FormControl>
                                <OnboardingTaskTemplateCard
                                  key={taskTemplate.title}
                                  taskTemplate={taskTemplate}
                                  selected={field.value === taskTemplate.title}
                                />
                              </FormLabel>
                            </FormItem>
                          ))}
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </If>

            <If condition={isLoading}>
              <PageSkeleton />
            </If>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-2">
          <Button
            variant="outline"
            type="button"
            disabled={isPending}
            className="gap-2"
            onClick={onSkip}
          >
            <If
              condition={isPending}
              fallback={<SkipForwardIcon className="size-4" />}
            >
              <Loader2Icon className="size-4 animate-spin" />
            </If>
            <span>Skip</span>
          </Button>

          <Button
            type="submit"
            className="gap-2"
            disabled={isPending || !watch('selected_template_title')}
          >
            <If
              condition={isPending}
              fallback={<RocketIcon className="size-4" />}
            >
              <Loader2Icon className="size-4 animate-spin" />
            </If>
            <span>Launch AppStudio</span>
          </Button>
        </div>
      </form>
    </Form>
  );
}
