import { useEffect, useRef } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

// Utility function to build URL with parameters
const buildUrlWithParams = (
  pathname: string,
  params: Record<string, string>,
) => {
  const urlParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      urlParams.append(key, value);
    }
  });
  const queryString = urlParams.toString();
  return queryString ? `${pathname}?${queryString}` : pathname;
};

interface UrlManagerProps {
  selectedConversation: string | null;
  conversationId?: string;
  mounted: boolean;
  setSelectedConversation: (id: string | null) => void;
  setInterruptConfirmation: (confirmation: any) => void;
}

export const useUrlManager = ({
  selectedConversation,
  conversationId,
  mounted,
  setSelectedConversation,
  setInterruptConfirmation,
}: UrlManagerProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Use refs to store the latest callback functions
  const setSelectedConversationRef = useRef(setSelectedConversation);
  const setInterruptConfirmationRef = useRef(setInterruptConfirmation);

  // Update refs when the callbacks change
  useEffect(() => {
    setSelectedConversationRef.current = setSelectedConversation;
    setInterruptConfirmationRef.current = setInterruptConfirmation;
  }, [setSelectedConversation, setInterruptConfirmation]);

  // Handle conversationId changes
  useEffect(() => {
    if (conversationId) {
      setSelectedConversationRef.current(conversationId);
      setInterruptConfirmationRef.current(null);
    }
  }, [conversationId]);

  // Handle URL updates
  useEffect(() => {
    if (selectedConversation && mounted) {
      const newUrl = buildUrlWithParams(window.location.pathname, {
        conversation: selectedConversation,
        ...(searchParams.get('initialMessage') && {
          initialMessage: searchParams.get('initialMessage')!,
        }),
      });
      router.replace(newUrl, { scroll: false });
    }
  }, [selectedConversation, mounted, router, searchParams]);
};
